package clients

import (
	"context"
	"fmt"
	"time"

	proto_order_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/order/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	shared_grpc "gitlab.zalopay.vn/phunn4/coupon-shared-libs/grpc"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
)

type OrderClient struct {
	Client proto_order_v1.OrderServiceClient
	conn   *shared_grpc.Client
}

func NewOrderClient(target string, cfg *config.GRPCConfig, logger *logging.Logger, metrics *metrics.Metrics, clientID, clientKey string) (*OrderClient, error) {
	client, err := shared_grpc.NewAuthenticatedClient(target, cfg, logger, metrics, clientID, clientKey)
	if err != nil {
		return nil, fmt.Errorf("failed to create shared gRPC client for order service: %w", err)
	}
	return &OrderClient{
		Client: proto_order_v1.NewOrderServiceClient(client.Conn),
		conn:   client,
	}, nil
}

func (c *OrderClient) Close() {
	c.conn.Close()
}

func (c *OrderClient) CreateOrder(ctx context.Context, req *proto_order_v1.CreateOrderRequest) (*proto_order_v1.CreateOrderResponse, error) {
	ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	return c.Client.CreateOrder(ctx, req)
}

func (c *OrderClient) GetOrder(ctx context.Context, req *proto_order_v1.GetOrderRequest) (*proto_order_v1.GetOrderResponse, error) {
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	return c.Client.GetOrder(ctx, req)
}

func (c *OrderClient) UpdateOrderStatus(ctx context.Context, req *proto_order_v1.UpdateOrderStatusRequest) (*proto_order_v1.UpdateOrderStatusResponse, error) {
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	return c.Client.UpdateOrderStatus(ctx, req)
}

func (c *OrderClient) ListOrders(ctx context.Context, req *proto_order_v1.ListOrdersRequest) (*proto_order_v1.ListOrdersResponse, error) {
	ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	return c.Client.ListOrders(ctx, req)
}

func (c *OrderClient) ListOrdersByVoucher(ctx context.Context, req *proto_order_v1.ListOrdersByVoucherRequest) (*proto_order_v1.ListOrdersByVoucherResponse, error) {
	ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	return c.Client.ListOrdersByVoucher(ctx, req)
}

func (c *OrderClient) GetUserOrderCount(ctx context.Context, req *proto_order_v1.GetUserOrderCountRequest) (*proto_order_v1.GetUserOrderCountResponse, error) {
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	return c.Client.GetUserOrderCount(ctx, req)
}

func (c *OrderClient) GetUserVoucherUsageCount(ctx context.Context, req *proto_order_v1.GetUserVoucherUsageCountRequest) (*proto_order_v1.GetUserVoucherUsageCountResponse, error) {
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	return c.Client.GetUserVoucherUsageCount(ctx, req)
}
