package clients

import (
	"context"
	"fmt"
	"time"

	product_proto_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/product/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	shared_grpc "gitlab.zalopay.vn/phunn4/coupon-shared-libs/grpc"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
)

type ProductClient struct {
	Client product_proto_v1.ProductServiceClient
	conn   *shared_grpc.Client
}

func NewProductClient(target string, cfg *config.GRPCConfig, logger *logging.Logger, metrics *metrics.Metrics, clientID, clientKey string) (*ProductClient, error) {
	client, err := shared_grpc.NewAuthenticatedClient(target, cfg, logger, metrics, clientID, clientKey)
	if err != nil {
		return nil, fmt.Errorf("failed to create shared gRPC client for product service: %w", err)
	}
	return &ProductClient{
		Client: product_proto_v1.NewProductServiceClient(client.Conn),
		conn:   client,
	}, nil
}

func (c *ProductClient) Close() {
	c.conn.Close()
}

func (c *ProductClient) GetProduct(ctx context.Context, req *product_proto_v1.GetProductRequest) (*product_proto_v1.GetProductResponse, error) {
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	return c.Client.GetProduct(ctx, req)
}

func (c *ProductClient) UpdateProduct(ctx context.Context, req *product_proto_v1.UpdateProductRequest) (*product_proto_v1.UpdateProductResponse, error) {
	ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	return c.Client.UpdateProduct(ctx, req)
}

func (c *ProductClient) ListProducts(ctx context.Context, req *product_proto_v1.ListProductsRequest) (*product_proto_v1.ListProductsResponse, error) {
	ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	return c.Client.ListProducts(ctx, req)
}

func (c *ProductClient) ListCategories(ctx context.Context, req *product_proto_v1.ListCategoriesRequest) (*product_proto_v1.ListCategoriesResponse, error) {
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	return c.Client.ListCategories(ctx, req)
}
