package handlers

import (
	"net/http"

	echoAdapter "github.com/TickLabVN/tonic/adapters/echo"
	"github.com/TickLabVN/tonic/core/docs"
	"github.com/labstack/echo/v4"
	"gitlab.zalopay.vn/phunn4/coupon-api-gateway/internal/clients"
	"gitlab.zalopay.vn/phunn4/coupon-api-gateway/internal/handlers/dto"
	"gitlab.zalopay.vn/phunn4/coupon-api-gateway/internal/utils"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/auth"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
)

type UserHandler struct {
	userClient *clients.UserClient
	logger     *logging.Logger
}

func NewUserHandler(u *clients.UserClient, l *logging.Logger) *UserHandler {
	return &UserHandler{userClient: u, logger: l}
}

func (h *UserHandler) RegisterProtectedRoutes(g *echo.Group, spec *docs.OpenApi) {
	meRoot := g.GET("/users/me", h.HandleGet<PERSON>e)
	echoAdapter.AddRoute[struct{}, dto.UserResponse](spec, meRoot, docs.OperationObject{Tags: []string{"Users API"}})
}

func (h *UserHandler) HandleGetMe(c echo.Context) error {
	userID, ok := auth.GetUserIDFromContext(c.Request().Context())
	if !ok {
		return c.JSON(http.StatusUnauthorized, utils.NewErrorResponse("invalid token: missing user_id"))
	}

	res, err := h.userClient.GetUser(c.Request().Context(), userID)
	if err != nil {
		return utils.HandleGRPCError(c, err, h.logger)
	}

	userResponse := dto.ToUserResponse(res.User)

	return c.JSON(http.StatusOK, userResponse)
}
