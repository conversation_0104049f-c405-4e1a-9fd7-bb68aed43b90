package dto

import (
	"time"

	proto_common_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/common/v1"
	proto_product_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/product/v1"
)

type UpdateProductRequest struct {
	Name          string  `json:"name" validate:"required,min=1,max=255"`
	Description   string  `json:"description"`
	Price         float64 `json:"price" validate:"required,gt=0"`
	CategoryID    uint64  `json:"category_id"`
	ImageURL      string  `json:"image_url"`
	StockQuantity int64   `json:"stock_quantity" validate:"gte=0"`
	Status        string  `json:"status" validate:"required,oneof=ACTIVE INACTIVE"`
	Brand         string  `json:"brand"`
	SKU           string  `json:"sku"`
}

type ProductResponse struct {
	ID            uint64            `json:"id"`
	Name          string            `json:"name"`
	Description   string            `json:"description"`
	Price         float64           `json:"price"`
	CategoryID    uint64            `json:"category_id"`
	ImageURL      string            `json:"image_url"`
	StockQuantity int64             `json:"stock_quantity"`
	Status        string            `json:"status"`
	Brand         string            `json:"brand"`
	SKU           string            `json:"sku"`
	CreatedAt     time.Time         `json:"created_at"`
	UpdatedAt     time.Time         `json:"updated_at"`
	Category      *CategoryResponse `json:"category,omitempty"`
}

type CategoryResponse struct {
	ID          uint64    `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	CreatedAt   time.Time `json:"created_at"`
}

type PaginationResponse struct {
	CurrentPage int32 `json:"current_page"`
	PageSize    int32 `json:"page_size"`
	TotalItems  int64 `json:"total_items"`
	TotalPages  int32 `json:"total_pages"`
	HasNext     bool  `json:"has_next"`
	HasPrevious bool  `json:"has_previous"`
}

type ListProductsRequest struct {
	Page       int     `json:"page" query:"page"`
	Limit      int     `json:"limit" query:"limit"`
	Search     string  `json:"search" query:"search"`
	CategoryID *uint64 `json:"category_id" query:"category_id"`
	Status     string  `json:"status" query:"status"`
	SortBy     string  `json:"sort_by" query:"sort_by"`
	SortOrder  string  `json:"sort_order" query:"sort_order"`
}

func ToProductResponse(product *proto_product_v1.Product) *ProductResponse {
	if product == nil {
		return nil
	}

	response := &ProductResponse{
		ID:            product.Id,
		Name:          product.Name,
		Description:   product.Description,
		Price:         product.Price,
		CategoryID:    product.CategoryId,
		ImageURL:      product.ImageUrl,
		StockQuantity: product.StockQuantity,
		Status:        convertProductStatusToString(product.Status),
		Brand:         product.Brand,
		SKU:           product.Sku,
		CreatedAt:     product.CreatedAt.AsTime(),
		UpdatedAt:     product.UpdatedAt.AsTime(),
	}

	if product.Category != nil {
		response.Category = ToCategoryResponse(product.Category)
	}

	return response
}

func ToCategoryResponse(category *proto_product_v1.Category) *CategoryResponse {
	if category == nil {
		return nil
	}

	return &CategoryResponse{
		ID:          category.Id,
		Name:        category.Name,
		Description: category.Description,
		CreatedAt:   category.CreatedAt.AsTime(),
	}
}

func ToPaginationResponse(pagination *proto_common_v1.PaginationResponse) PaginationResponse {
	if pagination == nil {
		return PaginationResponse{}
	}

	return PaginationResponse{
		CurrentPage: pagination.CurrentPage,
		PageSize:    pagination.PageSize,
		TotalItems:  pagination.TotalItems,
		TotalPages:  pagination.TotalPages,
		HasNext:     pagination.HasNext,
		HasPrevious: pagination.HasPrevious,
	}
}

func convertProductStatusToString(status proto_product_v1.ProductStatus) string {
	switch status {
	case proto_product_v1.ProductStatus_PRODUCT_STATUS_ACTIVE:
		return "ACTIVE"
	case proto_product_v1.ProductStatus_PRODUCT_STATUS_INACTIVE:
		return "INACTIVE"
	default:
		return "ACTIVE"
	}
}

func ConvertStringToProductStatus(status string) proto_product_v1.ProductStatus {
	switch status {
	case "ACTIVE":
		return proto_product_v1.ProductStatus_PRODUCT_STATUS_ACTIVE
	case "INACTIVE":
		return proto_product_v1.ProductStatus_PRODUCT_STATUS_INACTIVE
	default:
		return proto_product_v1.ProductStatus_PRODUCT_STATUS_ACTIVE
	}
}
