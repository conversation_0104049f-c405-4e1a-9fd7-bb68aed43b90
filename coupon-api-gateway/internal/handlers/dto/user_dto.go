package dto

import (
	"time"

	proto_user_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/user/v1"
)

type UserResponse struct {
	ID        uint64    `json:"id"`
	Email     string    `json:"email"`
	Name      string    `json:"name"`
	Role      string    `json:"role"`
	Type      string    `json:"type"`
	CreatedAt time.Time `json:"created_at"`
}

func ToUserResponse(user *proto_user_v1.User) *UserResponse {
	if user == nil {
		return nil
	}
	return &UserResponse{
		ID:        user.Id,
		Email:     user.Email,
		Name:      user.Name,
		Role:      user.Role,
		Type:      convertUserTypeToString(user.Type),
		CreatedAt: user.CreatedAt.AsTime(),
	}
}

func convertUserTypeToString(userType proto_user_v1.UserType) string {
	switch userType {
	case proto_user_v1.UserType_USER_TYPE_NEW:
		return "NEW"
	case proto_user_v1.UserType_USER_TYPE_VIP:
		return "VIP"
	default:
		return "NEW"
	}
}
