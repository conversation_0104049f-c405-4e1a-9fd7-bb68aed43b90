package utils

import (
	"net/http"
	"time"

	"github.com/labstack/echo/v4"
)

const (
	AccessTokenCookie = "access_token"
	AccessTokenExpiry = 1 * time.Hour
)

type CookieConfig struct {
	Domain   string
	Secure   bool
	HTTPOnly bool
	SameSite http.SameSite
}

func NewCookieConfig(domain string, secure bool) *CookieConfig {
	return &CookieConfig{
		Domain:   domain,
		Secure:   secure,
		HTTPOnly: true,
		SameSite: http.SameSiteStrictMode,
	}
}

func SetAuthCookies(c echo.Context, accessToken string, config *CookieConfig) {
	SetAccessTokenCookie(c, accessToken, config)
}

func SetAccessTokenCookie(c echo.Context, token string, config *CookieConfig) {
	cookie := &http.Cookie{
		Name:     AccessTokenCookie,
		Value:    token,
		Path:     "/",
		Domain:   config.Domain,
		Expires:  time.Now().Add(AccessTokenExpiry),
		MaxAge:   int(AccessTokenExpiry.Seconds()),
		Secure:   config.Secure,
		HttpOnly: config.HTTPOnly,
		SameSite: config.SameSite,
	}
	c.Set<PERSON>ookie(cookie)
}

func GetAccessTokenFromCookie(c echo.Context) (string, error) {
	cookie, err := c.Cookie(AccessTokenCookie)
	if err != nil {
		return "", err
	}
	return cookie.Value, nil
}

func ClearAuthCookies(c echo.Context, config *CookieConfig) {
	ClearAccessTokenCookie(c, config)
}

func ClearAccessTokenCookie(c echo.Context, config *CookieConfig) {
	cookie := &http.Cookie{
		Name:     AccessTokenCookie,
		Value:    "",
		Path:     "/",
		Domain:   config.Domain,
		Expires:  time.Unix(0, 0),
		MaxAge:   -1,
		Secure:   config.Secure,
		HttpOnly: config.HTTPOnly,
		SameSite: config.SameSite,
	}
	c.SetCookie(cookie)
}
