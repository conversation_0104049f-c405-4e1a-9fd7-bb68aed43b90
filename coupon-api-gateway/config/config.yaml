service:
  name: "api-gateway"
  version: "1.0.0"
  environment: "development"
  port: 8080
  client_id: "${API_GATEWAY_CLIENT_ID}"
  client_key: "${API_GATEWAY_CLIENT_KEY}"

downstream_services:
  user_service_addr: "user-service:50051"
  voucher_service_addr: "voucher-service:50051"
  product_service_addr: "product-service:50051"
  order_service_addr: "order-service:50051"

auth:
  jwt_secret: "${JWT_SECRET_KEY}"
  jwt_expiration: "1h"

jaeger:
  host: "jaeger"
  port: 6831

logging:
  level: "debug"
  format: "json"
