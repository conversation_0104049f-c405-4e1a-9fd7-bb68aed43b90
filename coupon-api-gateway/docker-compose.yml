services:
  # jaeger:
  #   image: jaegertracing/all-in-one:1.56
  #   container_name: jaeger
  #   restart: unless-stopped
  #   ports:
  #     - "16686:16686"
  #     - "6831:6831/udp"
  #   networks:
  #     - coupon-network

  api-gateway:
    build:
      context: .
      args:
        GITLAB_USER: ${GITLAB_USER}
        GITLAB_TOKEN: ${GITLAB_TOKEN}
    image: registry-gitlab.zalopay.vn/phunn4/coupon-api-gateway
    container_name: api-gateway
    # depends_on:
    #   - jaeger
    env_file:
      - .env
    ports:
      - "8080:8080"
    restart: unless-stopped
    healthcheck:
      test:
        [
          "CMD",
          "wget",
          "--no-verbose",
          "--tries=1",
          "--spider",
          "http://localhost:8080/health",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - coupon-network

networks:
  coupon-network:
    name: coupon-network
    driver: bridge
