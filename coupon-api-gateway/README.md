# Coupon API Gateway

This service provides an API gateway for the coupon microservice system.

## Requirements

- Go >= 1.24
- <PERSON><PERSON> and <PERSON>er Compose

## Configuration

Configuration values are loaded from `config/config.yaml` and environment variables. Any `${VAR_NAME}` placeholders in the YAML file are expanded from the current environment. Copy `.env.example` to `.env` and fill in the required secrets.

## Local Development

```bash
# Build the binary
make build

# Run the service locally
make run
```

## Docker Usage

To build the image and run the service:

```bash
# Build Docker image
make docker-build

# Run
make run
```

The service listens on `localhost:8080`.

### Docker Compose

A `docker-compose.yml` is provided to start the API gateway together with a
Jaeger instance for tracing.

```bash
make compose-up
```

This exposes the gateway on port `8080` and Jaeger UI on `16686`.
To stop the stack, run:

```bash
make compose-down
```
