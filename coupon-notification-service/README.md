# Coupon Notification Service

A simplified, event-driven notification service that handles **IN_APP notifications only** for the coupon microservice system. This service consumes events from Kafka topics and creates notifications that are stored directly in the database for user consumption.

## 🎯 Service Overview

The Coupon Notification Service is responsible for:

- **Event Processing**: Consuming business events from Kafka topics (voucher, order, user events)
- **Template Rendering**: Processing notification templates with dynamic data
- **IN_APP Notifications**: Creating and storing notifications directly in the database
- **gRPC API**: Providing notification management endpoints for other services
- **Real-time Delivery**: Immediate notification creation and storage

### Key Features

- ✅ **Simplified Architecture**: Only IN_APP notifications (email functionality removed)
- ✅ **Event-Driven**: Kafka-based event consumption
- ✅ **Template Engine**: Dynamic notification content generation
- ✅ **gRPC API**: Service-to-service communication
- ✅ **Database Storage**: PostgreSQL for persistence
- ✅ **Redis Caching**: Template and preference caching
- ✅ **Health Monitoring**: Built-in health checks and metrics
- ✅ **Auto-scaling**: Stateless design for horizontal scaling

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Kafka Topics  │    │  Notification    │    │   PostgreSQL    │
│                 │───▶│    Service       │───▶│   Database      │
│ • voucher-events│    │                  │    │                 │
│ • order-events  │    │ ┌──────────────┐ │    │ • notifications │
│ • user-events   │    │ │   Template   │ │    │ • templates     │
│ • notification- │    │ │   Engine     │ │    │                 │
│   events        │    │ └──────────────┘ │    └─────────────────┘
└─────────────────┘    │                  │              │
                       │ ┌──────────────┐ │              │
┌─────────────────┐    │ │   gRPC API   │ │              │
│   Other Services│◀───│ │   Handler    │ │              │
│                 │    │ └──────────────┘ │              │
│ • API Gateway   │    │                  │              │
│ • User Service  │    │ ┌──────────────┐ │    ┌─────────▼─────────┐
│ • Voucher Svc   │    │ │   Delivery   │ │    │      Redis        │
│ • Order Service │    │ │   Service    │ │    │                   │
└─────────────────┘    │ └──────────────┘ │    │ • Template Cache  │
                       └──────────────────┘    │ • Rate Limiting   │
                                               └───────────────────┘
```

### Simplified Notification Flow

1. **Event Reception**: Business events arrive via Kafka topics
2. **Template Processing**: Events trigger template rendering with dynamic data
3. **Notification Creation**: IN_APP notifications are created and stored in PostgreSQL
4. **Immediate Availability**: Notifications are instantly available for user consumption
5. **Status Management**: Notification status tracking (PENDING → SENT → READ)

## 🚀 Setup and Installation

### Prerequisites

- Docker and Docker Compose
- Go 1.21+ (for local development)
- PostgreSQL 15+
- Redis 7+
- Kafka (Confluent Platform)

### Quick Start with Docker

1. **Clone and navigate to the service directory**:

```bash
git clone <repository-url>
cd coupon-notification-service
```

2. **Set up environment variables**:

```bash
cp .env.example .env
# Edit .env with your configuration
```

3. **Start all services**:

```bash
docker-compose up -d
```

The service will automatically:

- ✅ Create required Kafka topics
- ✅ Run database migrations
- ✅ Seed default notification templates
- ✅ Start health checks

### Local Development Setup

1. **Install dependencies**:

```bash
go mod download
```

2. **Start infrastructure services**:

```bash
docker-compose up postgres-notification redis-notification kafka -d
```

3. **Run the service locally**:

```bash
go run cmd/server/main.go
```

## ⚙️ Configuration

### Environment Variables

| Variable            | Description               | Default                 | Required |
| ------------------- | ------------------------- | ----------------------- | -------- |
| `SERVICE_NAME`      | Service identifier        | `notification-service`  | No       |
| `SERVICE_VERSION`   | Service version           | `1.0.0`                 | No       |
| `HTTP_PORT`         | HTTP server port          | `8080`                  | No       |
| `GRPC_PORT`         | gRPC server port          | `50051`                 | No       |
| `POSTGRES_HOST`     | PostgreSQL host           | `postgres-notification` | Yes      |
| `POSTGRES_PORT`     | PostgreSQL port           | `5432`                  | No       |
| `POSTGRES_USER`     | Database user             | `coupon`                | Yes      |
| `POSTGRES_PASSWORD` | Database password         | -                       | Yes      |
| `POSTGRES_DB`       | Database name             | `notification_db`       | Yes      |
| `REDIS_HOST`        | Redis host                | `redis-notification`    | Yes      |
| `REDIS_PORT`        | Redis port                | `6379`                  | No       |
| `REDIS_PASSWORD`    | Redis password            | -                       | No       |
| `KAFKA_BROKERS`     | Kafka broker addresses    | `kafka:29092`           | Yes      |
| `KAFKA_GROUP_ID`    | Consumer group ID         | `notification-service`  | No       |
| `AUTH_SERVICE_ADDR` | Auth service gRPC address | `auth-service:50051`    | Yes      |
| `USER_SERVICE_ADDR` | User service gRPC address | `user-service:50051`    | Yes      |

## 📡 API Documentation

### gRPC Endpoints

#### 1. Send Notification

Creates and processes a new notification.

```protobuf
rpc SendNotification(SendNotificationRequest) returns (SendNotificationResponse);
```

**Request**:

```protobuf
message SendNotificationRequest {
  common.v1.RequestMetadata metadata = 1;
  NotificationType type = 2;
  string title = 3;
  string message = 4;
  google.protobuf.Timestamp scheduled_at = 5; // Optional
}
```

**Response**:

```protobuf
message SendNotificationResponse {
  common.v1.ResponseMetadata metadata = 1;
  Notification notification = 2;
  common.v1.ServiceError error = 3;
}
```

#### 2. Update Notification Status

Updates the status of an existing notification.

```protobuf
rpc UpdateNotificationStatus(UpdateNotificationStatusRequest) returns (UpdateNotificationStatusResponse);
```

#### 3. List Notifications

Retrieves notifications for a user with pagination.

```protobuf
rpc ListNotifications(ListNotificationsRequest) returns (ListNotificationsResponse);
```

#### 4. Health Check

Service health verification.

```protobuf
rpc HealthCheck(common.v1.HealthCheckRequest) returns (common.v1.HealthCheckResponse);
```

### Notification Types

| Type                 | Description                  | Usage                     |
| -------------------- | ---------------------------- | ------------------------- |
| `VOUCHER_CREATED`    | New voucher available        | When vouchers are created |
| `VOUCHER_EXPIRING`   | Voucher expiring soon        | Scheduled reminders       |
| `VOUCHER_USED`       | Voucher successfully applied | Order completion          |
| `ORDER_CONFIRMATION` | Order confirmed              | Order processing          |
| `VOUCHER_APPLIED`    | Voucher applied to order     | Checkout process          |
| `VOUCHER_FAILED`     | Voucher application failed   | Error handling            |
| `USER_WELCOME`       | Welcome new user             | User registration         |
| `USER_TYPE_UPGRADE`  | User tier upgrade            | User progression          |

### Notification Status Flow

```
PENDING → SENT → READ
    ↓
  FAILED
    ↓
CANCELLED
```

## 🔄 Kafka Event Consumption

### Consumed Topics

#### 1. voucher-events

Processes voucher lifecycle events.

**Event Types**:

- `voucher.created` - New voucher created
- `voucher.expiring` - Voucher expiring soon
- `voucher.used` - Voucher applied to order

**Example Event**:

```json
{
  "event_id": "uuid",
  "event_type": "voucher.created",
  "timestamp": "2024-01-15T10:30:00Z",
  "voucher_id": 123,
  "voucher_code": "SAVE20",
  "title": "20% Off Everything",
  "created_by": 456,
  "valid_from": "2024-01-15T00:00:00Z",
  "valid_until": "2024-01-31T23:59:59Z",
  "discount_value": 20.0,
  "user_eligibility_type": "ALL_USERS"
}
```

#### 2. order-events

Processes order-related events.

**Event Types**:

- `order.created` - New order placed
- `order.confirmed` - Order confirmed

#### 3. user-events

Processes user lifecycle events.

**Event Types**:

- `user.created` - New user registered
- `user.type_changed` - User tier upgraded

### Consumer Configuration

- **Group ID**: `notification-service`
- **Auto Offset Reset**: `earliest`
- **Enable Auto Commit**: `true`
- **Session Timeout**: `30s`
- **Heartbeat Interval**: `10s`

## 🗄️ Database Schema

### Tables

#### notifications

Stores all notification records.

```sql
CREATE TABLE notifications (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    type VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    scheduled_at TIMESTAMP,
    sent_at TIMESTAMP,
    read_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL,

    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_scheduled_at (scheduled_at),
    INDEX idx_created_at (created_at)
);
```

#### notification_templates

Stores notification templates for dynamic content generation.

```sql
CREATE TABLE notification_templates (
    id BIGSERIAL PRIMARY KEY,
    template_key VARCHAR(100) NOT NULL UNIQUE,
    type VARCHAR(50) NOT NULL,
    title_template TEXT NOT NULL,
    message_template TEXT NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL,

    INDEX idx_template_key (template_key),
    INDEX idx_type (type),
    INDEX idx_is_active (is_active)
);
```

### Database Migrations

Migrations are automatically applied on service startup using GORM's AutoMigrate feature.

## 🐳 Docker Deployment

### Services Overview

| Service                 | Port                   | Description                     |
| ----------------------- | ---------------------- | ------------------------------- |
| `notification-service`  | 8086:8080, 50057:50051 | Main application                |
| `postgres-notification` | 5438:5432              | PostgreSQL database             |
| `redis-notification`    | 6385:6379              | Redis cache                     |
| `kafka`                 | 9092:9092, 29092:29092 | Kafka message broker            |
| `kafka-ui`              | 8090:8080              | Kafka management UI             |
| `kafka-topic-init`      | -                      | Topic auto-creation (runs once) |

### Deployment Commands

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f notification-service

# Scale the service
docker-compose up -d --scale notification-service=3

# Stop services
docker-compose down

# Clean up (removes volumes)
docker-compose down -v
```

### Health Checks

All services include comprehensive health checks:

- **Application**: `GET /health`
- **PostgreSQL**: `pg_isready` command
- **Redis**: `redis-cli ping`
- **Kafka**: Topic listing verification

## 🔍 Health Check Endpoints

### HTTP Health Check

```bash
curl http://localhost:8086/health
```

**Response**:

```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z",
  "checks": {
    "database": "healthy",
    "redis": "healthy",
    "kafka": "healthy"
  }
}
```

### gRPC Health Check

```bash
grpcurl -plaintext localhost:50057 notification.v1.NotificationService/HealthCheck
```

### Metrics Endpoint

```bash
curl http://localhost:2118/metrics
```

## 🛠️ Troubleshooting

### Common Issues

#### 1. Service Won't Start

**Symptoms**: Container exits immediately or fails to start

**Solutions**:

```bash
# Check logs
docker-compose logs notification-service

# Verify dependencies
docker-compose ps

# Check environment variables
docker-compose config
```

#### 2. Database Connection Issues

**Symptoms**: `connection refused` or `authentication failed`

**Solutions**:

```bash
# Verify PostgreSQL is running
docker-compose ps postgres-notification

# Check database credentials
docker-compose exec postgres-notification psql -U coupon -d notification_db

# Reset database
docker-compose down postgres-notification
docker volume rm coupon-notification-service_postgres-notification-data
docker-compose up -d postgres-notification
```

#### 3. Kafka Connection Problems

**Symptoms**: Consumer group not connecting or events not processing

**Solutions**:

```bash
# Check Kafka status
docker-compose ps kafka

# Verify topics exist
docker-compose exec kafka kafka-topics --bootstrap-server localhost:9092 --list

# Recreate topics
docker-compose restart kafka-topic-init

# Check consumer group status
docker-compose exec kafka kafka-consumer-groups --bootstrap-server localhost:9092 --describe --group notification-service
```

#### 4. Redis Connection Issues

**Symptoms**: Template caching not working

**Solutions**:

```bash
# Test Redis connection
docker-compose exec redis-notification redis-cli ping

# Check Redis logs
docker-compose logs redis-notification

# Clear Redis cache
docker-compose exec redis-notification redis-cli FLUSHALL
```

#### 5. Template Rendering Errors

**Symptoms**: Notifications created with empty or malformed content

**Solutions**:

```bash
# Check template syntax in database
docker-compose exec postgres-notification psql -U coupon -d notification_db -c "SELECT * FROM notification_templates;"

# Reload templates
# Restart the service to reload default templates
docker-compose restart notification-service
```

### Debug Mode

Enable debug logging by setting:

```bash
export LOG_LEVEL=debug
docker-compose up -d
```

### Performance Monitoring

Monitor service performance:

```bash
# View resource usage
docker stats notification-service

# Check processing metrics
curl http://localhost:2118/metrics | grep notification

# Monitor Kafka lag
docker-compose exec kafka kafka-consumer-groups --bootstrap-server localhost:9092 --describe --group notification-service
```

### Log Analysis

```bash
# Follow application logs
docker-compose logs -f notification-service

# Filter error logs
docker-compose logs notification-service 2>&1 | grep ERROR

# Export logs for analysis
docker-compose logs --no-color notification-service > notification-service.log
```

## 📝 Development Notes

### Recent Simplifications (v2.0)

- ✅ **Removed Email Notifications**: Only IN_APP notifications supported
- ✅ **Removed User Preferences**: Simplified notification delivery
- ✅ **Removed Channel Selection**: All notifications are IN_APP by default
- ✅ **Simplified Database Schema**: Removed redundant tables and columns
- ✅ **Streamlined Configuration**: Removed email-related settings

### Template Variables

Templates support Go template syntax with helper functions:

- `{{formatCurrency .Amount}}` - Format currency values
- `{{formatDate .Date}}` - Format dates
- `{{formatDateTime .DateTime}}` - Format date and time
- `{{upper .Text}}` - Convert to uppercase
- `{{lower .Text}}` - Convert to lowercase
- `{{title .Text}}` - Convert to title case

---
