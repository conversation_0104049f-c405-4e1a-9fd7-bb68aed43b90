package consumers

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/segmentio/kafka-go"

	"gitlab.zalopay.vn/phunn4/coupon-notification-service/internal/model"
	"gitlab.zalopay.vn/phunn4/coupon-notification-service/internal/service"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	shared_kafka "gitlab.zalopay.vn/phunn4/coupon-shared-libs/kafka"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
)

type UserEventConsumer struct {
	consumer        *shared_kafka.Consumer
	notificationSvc service.NotificationService
	templateEngine  service.TemplateEngine
	logger          *logging.Logger
	config          *config.Config
}

func NewUserEventConsumer(
	cfg *config.Config,
	notificationSvc service.NotificationService,
	templateEngine service.TemplateEngine,
	logger *logging.Logger,
) *UserEventConsumer {
	consumer := shared_kafka.NewConsumer(&cfg.Kafka, cfg.Kafka.Topics.UserEvents, logger)

	return &UserEventConsumer{
		consumer:        consumer,
		notificationSvc: notificationSvc,
		templateEngine:  templateEngine,
		logger:          logger,
		config:          cfg,
	}
}

func (uec *UserEventConsumer) Start(ctx context.Context) error {
	log := uec.logger.WithContext(ctx)
	log.Info("Starting user event consumer")

	return uec.consumer.Consume(ctx, uec.handleMessage)
}

func (uec *UserEventConsumer) Stop() error {
	uec.logger.Info("Stopping user event consumer")
	return uec.consumer.Close()
}

func (uec *UserEventConsumer) handleMessage(ctx context.Context, message *kafka.Message) error {
	log := uec.logger.WithContext(ctx)

	var eventWrapper map[string]any
	if err := json.Unmarshal(message.Value, &eventWrapper); err != nil {
		log.Errorf("Failed to unmarshal event wrapper: %v", err)
		return err
	}

	eventType, ok := eventWrapper["event_type"].(string)
	if !ok {
		log.Error("Event type not found in message")
		return fmt.Errorf("event type not found")
	}

	payload, ok := eventWrapper["payload"]
	if !ok {
		log.Error("Payload not found in message")
		return fmt.Errorf("payload not found")
	}

	log.Infof("Processing user event: %s", eventType)

	switch eventType {
	case "user.created":
		return uec.handleUserCreated(ctx, payload)
	case "user.type_changed":
		return uec.handleUserTypeChanged(ctx, payload)
	case "user.login":
		return uec.handleUserLogin(ctx, payload)
	default:
		log.Warnf("Unknown user event type: %s", eventType)
		return nil
	}
}

func (uec *UserEventConsumer) handleUserCreated(ctx context.Context, payload any) error {
	log := uec.logger.WithContext(ctx)

	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return err
	}

	var event model.UserCreatedPayload
	if err := json.Unmarshal(payloadBytes, &event); err != nil {
		log.Errorf("Failed to unmarshal user created event: %v", err)
		return err
	}

	log.Infof("Processing user created event for user %d", event.UserID)

	variables := map[string]any{
		"UserID":   event.UserID,
		"Email":    event.Email,
		"Name":     event.Name,
		"UserType": event.UserType,
	}

	notification, err := uec.notificationSvc.CreateNotificationFromTemplate(
		ctx,
		event.UserID,
		"user_welcome",
		variables,
	)
	if err != nil {
		log.Errorf("Failed to create notification from template: %v", err)
		return err
	}

	log.Infof("Created welcome notification %d for user %d", notification.ID, event.UserID)
	return nil
}

func (uec *UserEventConsumer) handleUserTypeChanged(ctx context.Context, payload any) error {
	log := uec.logger.WithContext(ctx)

	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return err
	}

	var event model.UserTypeChangedPayload
	if err := json.Unmarshal(payloadBytes, &event); err != nil {
		log.Errorf("Failed to unmarshal user type changed event: %v", err)
		return err
	}

	log.Infof("Processing user type changed event for user %d: %s -> %s",
		event.UserID, event.OldType, event.NewType)

	variables := map[string]any{
		"UserID":  event.UserID,
		"Email":   event.Email,
		"Name":    event.Email,
		"OldType": event.OldType,
		"NewType": event.NewType,
		"Reason":  event.Reason,
	}

	notification, err := uec.notificationSvc.CreateNotificationFromTemplate(
		ctx,
		event.UserID,
		"user_type_upgrade",
		variables,
	)
	if err != nil {
		log.Errorf("Failed to create notification from template: %v", err)
		return err
	}

	log.Infof("Created user type upgrade notification %d for user %d", notification.ID, event.UserID)
	return nil
}

func (uec *UserEventConsumer) handleUserLogin(ctx context.Context, payload any) error {
	log := uec.logger.WithContext(ctx)

	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return err
	}

	var event struct {
		UserID    uint64 `json:"user_id"`
		Email     string `json:"email"`
		UserAgent string `json:"user_agent"`
		IPAddress string `json:"ip_address"`
	}
	if err := json.Unmarshal(payloadBytes, &event); err != nil {
		log.Errorf("Failed to unmarshal user login event: %v", err)
		return err
	}

	log.Infof("Processing user login event for user %d", event.UserID)

	log.Infof("User login event processed for user %d", event.UserID)
	return nil
}

type ConsumerManager struct {
	voucherConsumer *VoucherEventConsumer
	orderConsumer   *OrderEventConsumer
	userConsumer    *UserEventConsumer
	logger          *logging.Logger
}

func NewConsumerManager(
	cfg *config.Config,
	notificationSvc service.NotificationService,
	templateEngine service.TemplateEngine,
	logger *logging.Logger,
) *ConsumerManager {
	return &ConsumerManager{
		voucherConsumer: NewVoucherEventConsumer(cfg, notificationSvc, templateEngine, logger),
		orderConsumer:   NewOrderEventConsumer(cfg, notificationSvc, templateEngine, logger),
		userConsumer:    NewUserEventConsumer(cfg, notificationSvc, templateEngine, logger),
		logger:          logger,
	}
}

func (cm *ConsumerManager) Start(ctx context.Context) error {
	log := cm.logger.WithContext(ctx)
	log.Info("Starting all event consumers")

	go func() {
		if err := cm.voucherConsumer.Start(ctx); err != nil {
			log.Errorf("Voucher consumer failed: %v", err)
		}
	}()

	go func() {
		if err := cm.orderConsumer.Start(ctx); err != nil {
			log.Errorf("Order consumer failed: %v", err)
		}
	}()

	go func() {
		if err := cm.userConsumer.Start(ctx); err != nil {
			log.Errorf("User consumer failed: %v", err)
		}
	}()

	log.Info("All event consumers started")
	return nil
}

func (cm *ConsumerManager) Stop() error {
	cm.logger.Info("Stopping all event consumers")

	if err := cm.voucherConsumer.Stop(); err != nil {
		cm.logger.Errorf("Failed to stop voucher consumer: %v", err)
	}

	if err := cm.orderConsumer.Stop(); err != nil {
		cm.logger.Errorf("Failed to stop order consumer: %v", err)
	}

	if err := cm.userConsumer.Stop(); err != nil {
		cm.logger.Errorf("Failed to stop user consumer: %v", err)
	}

	cm.logger.Info("All event consumers stopped")
	return nil
}
