package repository

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"gitlab.zalopay.vn/phunn4/coupon-notification-service/internal/model"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/database"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/redis"
)

const (
	notificationCachePrefixByID     = "notification:id:"
	notificationCachePrefixByUserID = "notification:user:"
	notificationCacheTTL            = 5 * time.Minute
)

type NotificationRepository interface {
	CreateNotification(ctx context.Context, notification *model.Notification) error
	GetNotificationByID(ctx context.Context, id uint64) (*model.Notification, error)
	UpdateNotificationStatus(ctx context.Context, id uint64, status model.NotificationStatus) error
	MarkAsRead(ctx context.Context, id uint64) error
	ListNotificationsByUser(ctx context.Context, userID uint64, limit, offset int) ([]*model.Notification, int64, error)
	GetPendingNotifications(ctx context.Context, limit int) ([]*model.Notification, error)
	GetScheduledNotifications(ctx context.Context, before time.Time, limit int) ([]*model.Notification, error)

	CreateTemplate(ctx context.Context, template *model.NotificationTemplate) error
	GetTemplateByKey(ctx context.Context, templateKey string) (*model.NotificationTemplate, error)
	GetTemplatesByType(ctx context.Context, notificationType model.NotificationType) ([]*model.NotificationTemplate, error)
	UpdateTemplate(ctx context.Context, template *model.NotificationTemplate) error
	ListActiveTemplates(ctx context.Context) ([]*model.NotificationTemplate, error)
}

type notificationRepository struct {
	db     *database.DB
	redis  *redis.Client
	logger *logging.Logger
}

func NewNotificationRepository(db *database.DB, redis *redis.Client, logger *logging.Logger) NotificationRepository {
	return &notificationRepository{
		db:     db,
		redis:  redis,
		logger: logger,
	}
}

func (r *notificationRepository) CreateNotification(ctx context.Context, notification *model.Notification) error {
	err := r.db.WithContext(ctx).Create(notification).Error
	if err != nil {
		return err
	}

	go r.setNotificationInCache(context.Background(), notification)

	return nil
}

func (r *notificationRepository) GetNotificationByID(ctx context.Context, id uint64) (*model.Notification, error) {
	log := r.logger.WithContext(ctx)

	cacheKey := fmt.Sprintf("%s%d", notificationCachePrefixByID, id)
	if notification, err := r.getNotificationFromCache(ctx, cacheKey); err == nil {
		return notification, nil
	}

	var notification model.Notification
	err := r.db.WithContext(ctx).First(&notification, id).Error
	if err != nil {
		return nil, err
	}

	go r.setNotificationInCache(context.Background(), &notification)

	log.Debugf("Retrieved notification %d from database and cached", id)
	return &notification, nil
}

func (r *notificationRepository) UpdateNotificationStatus(ctx context.Context, id uint64, status model.NotificationStatus) error {
	updates := map[string]any{
		"status":     status,
		"updated_at": time.Now(),
	}

	if status == model.NotificationStatusSent {
		updates["sent_at"] = time.Now()
	}

	err := r.db.WithContext(ctx).Model(&model.Notification{}).Where("id = ?", id).Updates(updates).Error
	if err != nil {
		return err
	}

	cacheKey := fmt.Sprintf("%s%d", notificationCachePrefixByID, id)
	if err := r.redis.Del(ctx, cacheKey); err != nil {
		r.logger.WithContext(ctx).Warnf("Failed to invalidate cache for notification %d: %v", id, err)
	}

	return nil
}

func (r *notificationRepository) MarkAsRead(ctx context.Context, id uint64) error {
	now := time.Now()
	return r.db.WithContext(ctx).Model(&model.Notification{}).
		Where("id = ?", id).
		Updates(map[string]any{
			"status":     model.NotificationStatusRead,
			"read_at":    &now,
			"updated_at": now,
		}).Error
}

func (r *notificationRepository) ListNotificationsByUser(ctx context.Context, userID uint64, limit, offset int) ([]*model.Notification, int64, error) {
	var notifications []*model.Notification
	var total int64

	if err := r.db.WithContext(ctx).Model(&model.Notification{}).Where("user_id = ?", userID).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	err := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&notifications).Error

	return notifications, total, err
}

func (r *notificationRepository) GetPendingNotifications(ctx context.Context, limit int) ([]*model.Notification, error) {
	var notifications []*model.Notification
	err := r.db.WithContext(ctx).
		Where("status = ? AND (scheduled_at IS NULL OR scheduled_at <= ?)", model.NotificationStatusPending, time.Now()).
		Order("created_at ASC").
		Limit(limit).
		Find(&notifications).Error
	return notifications, err
}

func (r *notificationRepository) GetScheduledNotifications(ctx context.Context, before time.Time, limit int) ([]*model.Notification, error) {
	var notifications []*model.Notification
	err := r.db.WithContext(ctx).
		Where("status = ? AND scheduled_at IS NOT NULL AND scheduled_at <= ?", model.NotificationStatusPending, before).
		Order("scheduled_at ASC").
		Limit(limit).
		Find(&notifications).Error
	return notifications, err
}

func (r *notificationRepository) CreateTemplate(ctx context.Context, template *model.NotificationTemplate) error {
	return r.db.WithContext(ctx).Create(template).Error
}

func (r *notificationRepository) GetTemplateByKey(ctx context.Context, templateKey string) (*model.NotificationTemplate, error) {
	var template model.NotificationTemplate
	err := r.db.WithContext(ctx).Where("template_key = ? AND is_active = ?", templateKey, true).First(&template).Error
	if err != nil {
		return nil, err
	}
	return &template, nil
}

func (r *notificationRepository) GetTemplatesByType(ctx context.Context, notificationType model.NotificationType) ([]*model.NotificationTemplate, error) {
	var templates []*model.NotificationTemplate
	err := r.db.WithContext(ctx).Where("type = ? AND is_active = ?", notificationType, true).Find(&templates).Error
	return templates, err
}

func (r *notificationRepository) UpdateTemplate(ctx context.Context, template *model.NotificationTemplate) error {
	template.UpdatedAt = time.Now()
	return r.db.WithContext(ctx).Save(template).Error
}

func (r *notificationRepository) ListActiveTemplates(ctx context.Context) ([]*model.NotificationTemplate, error) {
	var templates []*model.NotificationTemplate
	err := r.db.WithContext(ctx).Where("is_active = ?", true).Find(&templates).Error
	return templates, err
}

func (r *notificationRepository) getNotificationFromCache(ctx context.Context, key string) (*model.Notification, error) {
	log := r.logger.WithContext(ctx)

	val, err := r.redis.Get(ctx, key)
	if err != nil || val == "" {
		return nil, err
	}

	log.Debugf("Cache hit for key: %s", key)
	var notification model.Notification
	if err := json.Unmarshal([]byte(val), &notification); err != nil {
		log.Errorf("Failed to unmarshal notification from cache for key %s: %v", key, err)
		return nil, err
	}
	return &notification, nil
}

func (r *notificationRepository) setNotificationInCache(ctx context.Context, notification *model.Notification) {
	log := r.logger.WithContext(ctx)

	notificationBytes, err := json.Marshal(notification)
	if err != nil {
		log.Errorf("Failed to marshal notification for caching (ID: %d): %v", notification.ID, err)
		return
	}

	keyByID := fmt.Sprintf("%s%d", notificationCachePrefixByID, notification.ID)
	if err := r.redis.Set(ctx, keyByID, notificationBytes, notificationCacheTTL); err != nil {
		log.Errorf("Failed to set notification cache for key %s: %v", keyByID, err)
	}
}
