#!/bin/bash

echo "Waiting for Ka<PERSON>ka to be ready..."
max_attempts=30
attempt=0

while [ $attempt -lt $max_attempts ]; do
  if kafka-topics --bootstrap-server kafka:29092 --list > /dev/null 2>&1; then
    echo "Kafka is ready!"
    break
  fi
  
  attempt=$((attempt + 1))
  echo "Kafka not ready yet, waiting... (attempt $attempt/$max_attempts)"
  sleep 5
done

if [ $attempt -eq $max_attempts ]; then
  echo "ERROR: Kafka failed to become ready after $max_attempts attempts"
  exit 1
fi

echo "Creating Kafka topics..."

topics=(
  "voucher-events"
  "order-events" 
  "user-events"
  "notification-events"
)

for topic in "${topics[@]}"; do
  echo "Checking topic: ${topic}"
  
  if kafka-topics --bootstrap-server kafka:29092 --list | grep -q "^${topic}$"; then
    echo "✓ Topic '${topic}' already exists"
  else
    echo "Creating topic '${topic}' with 3 partitions and replication factor 1..."
    
    if kafka-topics --bootstrap-server kafka:29092 \
      --create \
      --topic "${topic}" \
      --partitions 3 \
      --replication-factor 1; then
      echo "✓ Successfully created topic '${topic}'"
    else
      echo "✗ Failed to create topic '${topic}'"
      exit 1
    fi
  fi
done

echo "🎉 Topic initialization completed successfully!"

echo "Current Kafka topics:"
kafka-topics --bootstrap-server kafka:29092 --list
