BINARY=order-server

.PHONY: build run test docker-build compose-up compose-down

build:
	go build -o $(BINARY) ./cmd/server

run: build
	./$(BINARY)

test:
	go test ./...

docker-build:
	docker build -t coupon-notification-service .

compose-up:
	docker-compose up -d

compose-down:
	docker-compose down

create-topics:
	@echo "Creating Kafka topics..."
	chmod +x scripts/create-topics.sh
	./scripts/create-topics.sh
