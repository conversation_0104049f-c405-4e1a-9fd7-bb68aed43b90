package benchmark

import (
	"context"
	"fmt"
	"os"
	"testing"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/metadata"
	"gorm.io/gorm"

	notificationv1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/notification/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/benchmark"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/database"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/redis"
)

var (
	notificationGrpcSuite  *benchmark.GRPCBenchmarkSuite
	notificationDBSuite    *benchmark.DatabaseBenchmarkSuite
	notificationCacheSuite *benchmark.CacheBenchmarkSuite
	notificationTestDB     *database.DB
	notificationTestRedis  *redis.Client
	notificationGenerator  *benchmark.TestDataGenerator
)

func TestMain(m *testing.M) {
	if err := setupNotificationBenchmarkEnvironment(); err != nil {
		fmt.Printf("Failed to setup notification benchmark environment: %v\n", err)
		os.Exit(1)
	}

	code := m.Run()
	cleanupNotification()
	os.Exit(code)
}

func setupNotificationBenchmarkEnvironment() error {
	cfg, err := config.Load()
	if err != nil {
		return fmt.Errorf("failed to load config: %w", err)
	}

	logger := logging.New("info", "json")
	appMetrics := metrics.New("notification-service-benchmark")

	notificationTestDB, err = database.NewPostgresDB(&cfg.Database, logger, appMetrics)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}

	notificationTestRedis = redis.NewClient(&cfg.Redis, logger, appMetrics)

	grpcConfig := &benchmark.GRPCBenchmarkConfig{
		ServiceAddress: "localhost:50057",
		ServiceName:    "notification-service",
		Timeout:        5 * time.Second,
	}

	notificationGrpcSuite, err = benchmark.NewGRPCBenchmarkSuite(grpcConfig)
	if err != nil {
		return fmt.Errorf("failed to create gRPC benchmark suite: %w", err)
	}

	dbConfig := &benchmark.DatabaseBenchmarkConfig{
		ServiceName:    "notification-service",
		DatabaseType:   "postgres",
		ConnectionPool: 10,
		QueryTimeout:   5 * time.Second,
	}
	notificationDBSuite = benchmark.NewDatabaseBenchmarkSuite(dbConfig, notificationTestDB.DB)

	notificationCacheSuite = benchmark.NewCacheBenchmarkSuite("notification-service", notificationTestRedis)
	notificationGenerator = benchmark.NewTestDataGenerator()

	return nil
}

func cleanupNotification() {
	if notificationGrpcSuite != nil {
		notificationGrpcSuite.Close()
	}
	if notificationTestDB != nil {
		sqlDB, _ := notificationTestDB.DB.DB()
		sqlDB.Close()
	}
	if notificationTestRedis != nil {
		notificationTestRedis.Close()
	}
}

func BenchmarkNotificationService_gRPC_SendNotification(b *testing.B) {
	notificationGrpcSuite.BenchmarkUnaryCall(b, func(ctx context.Context, conn *grpc.ClientConn) error {
		client := notificationv1.NewNotificationServiceClient(conn)
		ctx = addNotificationAuthMetadata(ctx)

		req := &notificationv1.SendNotificationRequest{
			Type:    notificationv1.NotificationType_NOTIFICATION_TYPE_VOUCHER_CREATED,
			Title:   "Test Notification",
			Message: "This is a benchmark test notification",
		}

		_, err := client.SendNotification(ctx, req)
		return err
	})
}

func BenchmarkNotificationService_gRPC_ListNotifications(b *testing.B) {
	notificationGrpcSuite.BenchmarkUnaryCall(b, func(ctx context.Context, conn *grpc.ClientConn) error {
		client := notificationv1.NewNotificationServiceClient(conn)
		ctx = addNotificationAuthMetadata(ctx)

		req := &notificationv1.ListNotificationsRequest{}

		_, err := client.ListNotifications(ctx, req)
		return err
	})
}

func BenchmarkNotificationService_gRPC_UpdateNotificationStatus(b *testing.B) {
	notificationGrpcSuite.BenchmarkUnaryCall(b, func(ctx context.Context, conn *grpc.ClientConn) error {
		client := notificationv1.NewNotificationServiceClient(conn)
		ctx = addNotificationAuthMetadata(ctx)

		req := &notificationv1.UpdateNotificationStatusRequest{
			NotificationId: uint64(notificationGenerator.GenerateNotificationID()),
			Status:         notificationv1.NotificationStatus_NOTIFICATION_STATUS_SENT,
		}

		_, err := client.UpdateNotificationStatus(ctx, req)
		return err
	})
}

func BenchmarkNotificationService_gRPC_CreateNotificationFromTemplate(b *testing.B) {
	notificationGrpcSuite.BenchmarkUnaryCall(b, func(ctx context.Context, conn *grpc.ClientConn) error {
		client := notificationv1.NewNotificationServiceClient(conn)
		ctx = addNotificationAuthMetadata(ctx)

		req := &notificationv1.CreateNotificationFromTemplateRequest{
			TemplateKey: "voucher_created",
		}

		_, err := client.CreateNotificationFromTemplate(ctx, req)
		return err
	})
}

func BenchmarkNotificationService_Database_CreateNotification(b *testing.B) {
	notificationDBSuite.BenchmarkQuery(b, "create_notification", func(ctx context.Context, db *gorm.DB) error {
		notificationData := map[string]any{
			"user_id":    notificationGenerator.GenerateUserID(),
			"type":       "IN_APP",
			"title":      "Test Notification",
			"message":    "This is a benchmark test notification",
			"data":       `{"test": "data"}`,
			"is_read":    false,
			"created_at": time.Now(),
			"updated_at": time.Now(),
		}
		return db.Table("notifications").Create(notificationData).Error
	})
}

func BenchmarkNotificationService_Database_GetUserNotifications(b *testing.B) {
	notificationDBSuite.BenchmarkQuery(b, "get_user_notifications", func(ctx context.Context, db *gorm.DB) error {
		userID := notificationGenerator.GenerateUserID()
		var notifications []struct {
			ID        int       `gorm:"column:id"`
			Type      string    `gorm:"column:type"`
			Title     string    `gorm:"column:title"`
			Message   string    `gorm:"column:message"`
			IsRead    bool      `gorm:"column:is_read"`
			CreatedAt time.Time `gorm:"column:created_at"`
		}
		return db.Table("notifications").
			Where("user_id = ?", userID).
			Order("created_at DESC").
			Limit(20).Find(&notifications).Error
	})
}

func BenchmarkNotificationService_Database_MarkAsRead(b *testing.B) {
	notificationDBSuite.BenchmarkQuery(b, "mark_as_read", func(ctx context.Context, db *gorm.DB) error {
		notificationID := notificationGenerator.GenerateNotificationID()
		return db.Table("notifications").
			Where("id = ?", notificationID).
			Updates(map[string]any{
				"is_read":    true,
				"read_at":    time.Now(),
				"updated_at": time.Now(),
			}).Error
	})
}

func BenchmarkNotificationService_Database_MarkAllAsRead(b *testing.B) {
	notificationDBSuite.BenchmarkQuery(b, "mark_all_as_read", func(ctx context.Context, db *gorm.DB) error {
		userID := notificationGenerator.GenerateUserID()
		return db.Table("notifications").
			Where("user_id = ? AND is_read = false", userID).
			Updates(map[string]any{
				"is_read":    true,
				"read_at":    time.Now(),
				"updated_at": time.Now(),
			}).Error
	})
}

func BenchmarkNotificationService_Database_GetUnreadCount(b *testing.B) {
	notificationDBSuite.BenchmarkQuery(b, "get_unread_count", func(ctx context.Context, db *gorm.DB) error {
		userID := notificationGenerator.GenerateUserID()
		var count int64
		return db.Table("notifications").
			Where("user_id = ? AND is_read = false", userID).
			Count(&count).Error
	})
}

func BenchmarkNotificationService_Database_BulkCreateNotifications(b *testing.B) {
	notificationDBSuite.BenchmarkBulkInsert(b, "notifications", 50, func() any {
		return map[string]any{
			"user_id":    notificationGenerator.GenerateUserID(),
			"type":       "IN_APP",
			"title":      fmt.Sprintf("Notification %d", notificationGenerator.GenerateNotificationID()),
			"message":    "Bulk notification message",
			"data":       `{"bulk": "true"}`,
			"is_read":    false,
			"created_at": time.Now(),
			"updated_at": time.Now(),
		}
	})
}

func BenchmarkNotificationService_Cache_GetUserNotifications(b *testing.B) {
	framework := benchmark.NewBenchmarkFramework(&benchmark.BenchmarkConfig{
		ServiceName:    "notification-service",
		TestName:       "cache_get_user_notifications",
		MetricsEnabled: true,
	})

	operation := func(ctx context.Context) error {
		userID := notificationGenerator.GenerateUserID()
		cacheKey := fmt.Sprintf("user_notifications:%d", userID)

		_, err := notificationTestRedis.Get(ctx, cacheKey)
		if err == nil {
			return nil
		}

		notificationsData := []map[string]any{
			notificationGenerator.GenerateNotificationData(),
			notificationGenerator.GenerateNotificationData(),
		}

		return notificationTestRedis.Set(ctx, cacheKey, notificationsData, 5*time.Minute)
	}

	framework.RunBenchmark(b, operation)
}

func BenchmarkNotificationService_Cache_GetUnreadCount(b *testing.B) {
	notificationCacheSuite.BenchmarkCacheGet(b, func(ctx context.Context, key string) error {
		userID := notificationGenerator.GenerateUserID()
		cacheKey := fmt.Sprintf("unread_count:%d", userID)
		_, err := notificationTestRedis.Get(ctx, cacheKey)
		return err
	})
}

func BenchmarkNotificationService_Cache_SetUnreadCount(b *testing.B) {
	notificationCacheSuite.BenchmarkCacheSet(b, func(ctx context.Context, key string, value any) error {
		userID := notificationGenerator.GenerateUserID()
		cacheKey := fmt.Sprintf("unread_count:%d", userID)
		count := notificationGenerator.GenerateUnreadCount()
		return notificationTestRedis.Set(ctx, cacheKey, count, 10*time.Minute)
	})
}

func BenchmarkNotificationService_TemplateRendering(b *testing.B) {
	framework := benchmark.NewBenchmarkFramework(&benchmark.BenchmarkConfig{
		ServiceName:    "notification-service",
		TestName:       "template_rendering",
		MetricsEnabled: true,
	})

	operation := func(ctx context.Context) error {
		templates := []string{
			"voucher_expiry",
			"order_confirmation",
			"user_welcome",
			"password_reset",
		}

		template := templates[notificationGenerator.Rand().Intn(len(templates))]

		time.Sleep(time.Microsecond * time.Duration(notificationGenerator.Rand().Intn(1000)))

		cacheKey := fmt.Sprintf("template:%s:%d", template, notificationGenerator.GenerateUserID())
		return notificationTestRedis.Set(ctx, cacheKey, "rendered_content", 30*time.Minute)
	}

	framework.RunBenchmark(b, operation)
}

func BenchmarkNotificationService_ConcurrentLoad(b *testing.B) {
	scenario := &benchmark.LoadTestScenario{
		Name:        "notification-service-mixed-load",
		Duration:    30 * time.Second,
		Concurrency: 10,
		RampUpTime:  5 * time.Second,
		Operations: []benchmark.LoadTestOperation{
			{
				Name:   "send_notification",
				Weight: 50,
				Execute: func(ctx context.Context) error {
					ctx = addNotificationAuthMetadata(ctx)

					conn, err := grpc.NewClient("localhost:50057", grpc.WithTransportCredentials(insecure.NewCredentials()))
					if err != nil {
						return err
					}
					defer conn.Close()

					client := notificationv1.NewNotificationServiceClient(conn)
					req := &notificationv1.SendNotificationRequest{
						Type:    notificationv1.NotificationType_NOTIFICATION_TYPE_VOUCHER_CREATED,
						Title:   "Test Notification",
						Message: "Benchmark test message",
					}
					_, err = client.SendNotification(ctx, req)
					return err
				},
			},
			{
				Name:   "list_notifications",
				Weight: 30,
				Execute: func(ctx context.Context) error {
					ctx = addNotificationAuthMetadata(ctx)

					conn, err := grpc.NewClient("localhost:50057", grpc.WithTransportCredentials(insecure.NewCredentials()))
					if err != nil {
						return err
					}
					defer conn.Close()

					client := notificationv1.NewNotificationServiceClient(conn)
					req := &notificationv1.ListNotificationsRequest{}
					_, err = client.ListNotifications(ctx, req)
					return err
				},
			},
			{
				Name:   "update_notification_status",
				Weight: 20,
				Execute: func(ctx context.Context) error {
					ctx = addNotificationAuthMetadata(ctx)

					conn, err := grpc.NewClient("localhost:50057", grpc.WithTransportCredentials(insecure.NewCredentials()))
					if err != nil {
						return err
					}
					defer conn.Close()

					client := notificationv1.NewNotificationServiceClient(conn)
					req := &notificationv1.UpdateNotificationStatusRequest{
						NotificationId: uint64(notificationGenerator.GenerateNotificationID()),
						Status:         notificationv1.NotificationStatus_NOTIFICATION_STATUS_SENT,
					}
					_, err = client.UpdateNotificationStatus(ctx, req)
					return err
				},
			},
		},
	}

	runner := benchmark.NewLoadTestRunner(scenario)
	runner.RunLoadTest(b)
}

func addNotificationAuthMetadata(ctx context.Context) context.Context {
	md := metadata.New(map[string]string{
		"client-id":  "notification-service-benchmark",
		"client-key": "benchmark-key",
	})
	return metadata.NewOutgoingContext(ctx, md)
}
