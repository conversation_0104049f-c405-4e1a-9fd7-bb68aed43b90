package grpc

import (
	"context"
	"fmt"
	"time"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	grpc_opentracing "github.com/grpc-ecosystem/go-grpc-middleware/tracing/opentracing"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/keepalive"

	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
)

type Client struct {
	Conn    *grpc.ClientConn
	config  *config.GRPCConfig
	logger  *logging.Logger
	metrics *metrics.Metrics
}

func NewClient(target string, cfg *config.GRPCConfig, logger *logging.Logger, metrics *metrics.Metrics) (*Client, error) {
	opts := []grpc.DialOption{
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithKeepaliveParams(keepalive.ClientParameters{
			Time:                cfg.KeepaliveTime,
			Timeout:             cfg.KeepaliveTimeout,
			PermitWithoutStream: true,
		}),
		grpc.WithDefaultCallOptions(
			grpc.MaxCallRecvMsgSize(cfg.MaxReceiveSize),
			grpc.MaxCallSendMsgSize(cfg.MaxSendSize),
		),
		grpc.WithUnaryInterceptor(grpc_middleware.ChainUnaryClient(
			grpc_opentracing.UnaryClientInterceptor(),
			clientLoggingInterceptor(logger),
			clientMetricsInterceptor(metrics),
		)),
	}

	conn, err := grpc.NewClient(target, opts...)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to %s: %w", target, err)
	}

	return &Client{
		Conn:    conn,
		config:  cfg,
		logger:  logger,
		metrics: metrics,
	}, nil
}

func (c *Client) Close() error {
	return c.Conn.Close()
}

func clientLoggingInterceptor(logger *logging.Logger) grpc.UnaryClientInterceptor {
	return func(ctx context.Context, method string, req, reply any, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {
		start := time.Now()

		logger.WithContext(ctx).WithFields(logrus.Fields{
			"method": method,
		}).Debug("gRPC client request started")

		err := invoker(ctx, method, req, reply, cc, opts...)

		duration := time.Since(start)
		fields := logrus.Fields{
			"method":   method,
			"duration": duration.String(),
		}

		if err != nil {
			fields["error"] = err.Error()
			logger.WithContext(ctx).WithFields(fields).Error("gRPC client request failed")
		} else {
			logger.WithContext(ctx).WithFields(fields).Debug("gRPC client request completed")
		}

		return err
	}
}

func clientMetricsInterceptor(metrics *metrics.Metrics) grpc.UnaryClientInterceptor {
	return func(ctx context.Context, method string, req, reply any, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {
		start := time.Now()

		err := invoker(ctx, method, req, reply, cc, opts...)

		duration := time.Since(start)
		status := "success"
		if err != nil {
			status = "error"
		}

		metrics.RecordGRPCRequest("grpc-client", method, status, duration)

		return err
	}
}
