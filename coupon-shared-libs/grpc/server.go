package grpc

import (
	"context"
	"fmt"
	"net"
	"time"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	grpc_auth "github.com/grpc-ecosystem/go-grpc-middleware/auth"
	grpc_recovery "github.com/grpc-ecosystem/go-grpc-middleware/recovery"
	grpc_ctxtags "github.com/grpc-ecosystem/go-grpc-middleware/tags"
	grpc_opentracing "github.com/grpc-ecosystem/go-grpc-middleware/tracing/opentracing"
	"github.com/opentracing/opentracing-go"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/keepalive"

	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
)

type Server struct {
	Server  *grpc.Server
	config  *config.GRPCConfig
	logger  *logging.Logger
	metrics *metrics.Metrics
}

func NewServer(cfg *config.GRPCConfig, logger *logging.Logger, metrics *metrics.Metrics, authFunc grpc_auth.AuthFunc, serviceName string) *Server {
	opts := []grpc.ServerOption{
		grpc.KeepaliveParams(keepalive.ServerParameters{
			Time:    cfg.KeepaliveTime,
			Timeout: cfg.KeepaliveTimeout,
		}),
		grpc.KeepaliveEnforcementPolicy(keepalive.EnforcementPolicy{
			MinTime:             cfg.KeepaliveTime,
			PermitWithoutStream: true,
		}),
		grpc.MaxRecvMsgSize(cfg.MaxReceiveSize),
		grpc.MaxSendMsgSize(cfg.MaxSendSize),
		grpc.UnaryInterceptor(grpc_middleware.ChainUnaryServer(
			grpc_ctxtags.UnaryServerInterceptor(),
			grpc_opentracing.UnaryServerInterceptor(),
			grpc_recovery.UnaryServerInterceptor(),
			grpc_auth.UnaryServerInterceptor(authFunc),
			loggingInterceptor(logger),
			metricsInterceptor(metrics, serviceName),
		)),
	}

	server := grpc.NewServer(opts...)

	return &Server{
		Server:  server,
		config:  cfg,
		logger:  logger,
		metrics: metrics,
	}
}

func (s *Server) Start() error {
	address := fmt.Sprintf("%s:%d", s.config.Host, s.config.Port)
	listener, err := net.Listen("tcp", address)
	if err != nil {
		return fmt.Errorf("failed to listen on %s: %w", address, err)
	}

	s.logger.WithFields(logging.Fields{
		"address": address,
	}).Info("Starting gRPC server")

	return s.Server.Serve(listener)
}

func (s *Server) Stop() {
	s.logger.Info("Shutting down gRPC server")
	s.Server.GracefulStop()
}

func loggingInterceptor(logger *logging.Logger) grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req any, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (any, error) {
		start := time.Now()

		span := opentracing.SpanFromContext(ctx)
		if span != nil {
			ctx = opentracing.ContextWithSpan(ctx, span)
		}

		logger.WithContext(ctx).WithFields(logrus.Fields{
			"method": info.FullMethod,
		}).Info("gRPC request started")

		resp, err := handler(ctx, req)

		duration := time.Since(start)
		fields := logrus.Fields{
			"method":   info.FullMethod,
			"duration": duration.String(),
		}

		if err != nil {
			fields["error"] = err.Error()
			logger.WithContext(ctx).WithFields(fields).Error("gRPC request failed")
		} else {
			logger.WithContext(ctx).WithFields(fields).Info("gRPC request completed")
		}

		return resp, err
	}
}

func metricsInterceptor(metrics *metrics.Metrics, serviceName string) grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req any, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (any, error) {
		start := time.Now()

		resp, err := handler(ctx, req)

		duration := time.Since(start)
		status := "success"
		if err != nil {
			status = "error"
		}

		metrics.RecordGRPCRequest(serviceName, info.FullMethod, status, duration)

		return resp, err
	}
}
