package errors

import (
	"net/http"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type ErrorType string

const (
	ErrorTypeValidation   ErrorType = "VALIDATION_ERROR"
	ErrorTypeNotFound     ErrorType = "NOT_FOUND"
	ErrorTypeUnauthorized ErrorType = "UNAUTHORIZED"
	ErrorTypeForbidden    ErrorType = "FORBIDDEN"
	ErrorTypeConflict     ErrorType = "CONFLICT"
	ErrorTypeInternal     ErrorType = "INTERNAL_ERROR"
	ErrorTypeTimeout      ErrorType = "TIMEOUT"
	ErrorTypeUnavailable  ErrorType = "SERVICE_UNAVAILABLE"
)

type AppError struct {
	Type    ErrorType           `json:"type"`
	Message string                 `json:"message"`
	Code    int                       `json:"code"`
	Details map[string]any `json:"details,omitempty"`
}

func (e *AppError) Error() string {
	return e.Message
}

func NewValidationError(message string, details map[string]any) *AppError {
	return &AppError{
		Type:    ErrorTypeValidation,
		Message: message,
		Code:    http.StatusBadRequest,
		Details: details,
	}
}

func NewNotFoundError(message string) *AppError {
	return &AppError{
		Type:    ErrorTypeNotFound,
		Message: message,
		Code:    http.StatusNotFound,
	}
}

func NewUnauthorizedError(message string) *AppError {
	return &AppError{
		Type:    ErrorTypeUnauthorized,
		Message: message,
		Code:    http.StatusUnauthorized,
	}
}

func NewForbiddenError(message string) *AppError {
	return &AppError{
		Type:    ErrorTypeForbidden,
		Message: message,
		Code:    http.StatusForbidden,
	}
}

func NewConflictError(message string) *AppError {
	return &AppError{
		Type:    ErrorTypeConflict,
		Message: message,
		Code:    http.StatusConflict,
	}
}

func NewInternalError(message string) *AppError {
	return &AppError{
		Type:    ErrorTypeInternal,
		Message: message,
		Code:    http.StatusInternalServerError,
	}
}

func NewTimeoutError(message string) *AppError {
	return &AppError{
		Type:    ErrorTypeTimeout,
		Message: message,
		Code:    http.StatusRequestTimeout,
	}
}

func NewUnavailableError(message string) *AppError {
	return &AppError{
		Type:    ErrorTypeUnavailable,
		Message: message,
		Code:    http.StatusServiceUnavailable,
	}
}

func ToGRPCError(err error) error {
	if appErr, ok := err.(*AppError); ok {
		var code codes.Code
		switch appErr.Type {
		case ErrorTypeValidation:
			code = codes.InvalidArgument
		case ErrorTypeNotFound:
			code = codes.NotFound
		case ErrorTypeUnauthorized:
			code = codes.Unauthenticated
		case ErrorTypeForbidden:
			code = codes.PermissionDenied
		case ErrorTypeConflict:
			code = codes.AlreadyExists
		case ErrorTypeTimeout:
			code = codes.DeadlineExceeded
		case ErrorTypeUnavailable:
			code = codes.Unavailable
		default:
			code = codes.Internal
		}
		return status.Error(code, appErr.Message)
	}
	return status.Error(codes.Internal, err.Error())
}

func FromGRPCError(err error) *AppError {
	if st, ok := status.FromError(err); ok {
		var errorType ErrorType
		var httpCode int

		switch st.Code() {
		case codes.InvalidArgument:
			errorType = ErrorTypeValidation
			httpCode = http.StatusBadRequest
		case codes.NotFound:
			errorType = ErrorTypeNotFound
			httpCode = http.StatusNotFound
		case codes.Unauthenticated:
			errorType = ErrorTypeUnauthorized
			httpCode = http.StatusUnauthorized
		case codes.PermissionDenied:
			errorType = ErrorTypeForbidden
			httpCode = http.StatusForbidden
		case codes.AlreadyExists:
			errorType = ErrorTypeConflict
			httpCode = http.StatusConflict
		case codes.DeadlineExceeded:
			errorType = ErrorTypeTimeout
			httpCode = http.StatusRequestTimeout
		case codes.Unavailable:
			errorType = ErrorTypeUnavailable
			httpCode = http.StatusServiceUnavailable
		default:
			errorType = ErrorTypeInternal
			httpCode = http.StatusInternalServerError
		}

		return &AppError{
			Type:    errorType,
			Message: st.Message(),
			Code:    httpCode,
		}
	}

	return NewInternalError(err.Error())
}
