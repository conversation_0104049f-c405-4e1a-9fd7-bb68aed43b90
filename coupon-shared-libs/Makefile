.PHONY: build test lint clean proto deps fmt vet tidy

GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOLINT=golangci-lint

BINARY_NAME=coupon-shared-libs

build:
	$(GOBUILD) -o $(BINARY_NAME) ./...

test:
	$(GOTEST) ./...

lint:
	$(GOLINT) run ./...

clean:
	$(GOCLEAN)
	rm -f $(BINARY_NAME)

proto:
	protoc --go_out=. --go-grpc_out=. $(PROTO_FILES)

deps:
	$(GOGET) ./...

fmt:
	gofmt -w $(shell find . -name '*.go' -not -path './vendor/*')

vet:
	$(GOCMD) vet ./...

tidy:
	$(GOCMD) mod tidy

