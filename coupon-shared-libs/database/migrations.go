package database

import (
	"fmt"

	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
)

type AutoMigrator struct {
	db     *DB
	logger *logging.Logger
}

func NewAutoMigrator(db *DB, logger *logging.Logger) *AutoMigrator {
	return &AutoMigrator{
		db:     db,
		logger: logger,
	}
}

func (am *AutoMigrator) AutoMigrate(models ...any) error {
	am.logger.Info("Starting GORM AutoMigrate for database schema")

	if len(models) == 0 {
		am.logger.Info("No models provided for migration")
		return nil
	}

	if err := am.db.AutoMigrate(models...); err != nil {
		return fmt.Errorf("failed to auto-migrate database schema: %w", err)
	}

	am.logger.Infof("Successfully migrated %d models", len(models))
	return nil
}

func (am *AutoMigrator) AutoMigrateWithSeeds(models []any, seedFuncs ...func(*DB) error) error {
	if err := am.AutoMigrate(models...); err != nil {
		return err
	}

	for i, seedFunc := range seedFuncs {
		am.logger.Infof("Running seed function %d", i+1)
		if err := seedFunc(am.db); err != nil {
			return fmt.Errorf("failed to run seed function %d: %w", i+1, err)
		}
	}

	am.logger.Info("Database migration and seeding completed successfully")
	return nil
}
