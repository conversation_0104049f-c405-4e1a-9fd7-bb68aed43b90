package kafka

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/segmentio/kafka-go"
	"github.com/sirupsen/logrus"

	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
)

type Producer struct {
	writer *kafka.Writer
	config *config.KafkaConfig
	logger *logging.Logger
}

type Message struct {
	Key       string            `json:"key"`
	Value     any               `json:"value"`
	Headers   map[string]string `json:"headers"`
	Timestamp time.Time         `json:"timestamp"`
}

func NewProducer(cfg *config.KafkaConfig, logger *logging.Logger) *Producer {
	writer := &kafka.Writer{
		Addr:     kafka.TCP(cfg.Brokers...),
		Balancer: &kafka.LeastBytes{},
	}

	return &Producer{
		writer: writer,
		config: cfg,
		logger: logger,
	}
}

func (p *Producer) SendMessage(ctx context.Context, topic string, msg *Message) error {
	valueBytes, err := json.Marshal(msg.Value)
	if err != nil {
		return fmt.Errorf("failed to marshal message value: %w", err)
	}

	headers := make([]kafka.Header, 0, len(msg.Headers))
	for k, v := range msg.Headers {
		headers = append(headers, kafka.Header{
			Key:   k,
			Value: []byte(v),
		})
	}

	kafkaMsg := kafka.Message{
		Topic:   topic,
		Key:     []byte(msg.Key),
		Value:   valueBytes,
		Headers: headers,
		Time:    msg.Timestamp,
	}

	start := time.Now()
	err = p.writer.WriteMessages(ctx, kafkaMsg)
	duration := time.Since(start)

	fields := logrus.Fields{
		"topic":    topic,
		"key":      msg.Key,
		"duration": duration.String(),
	}

	if err != nil {
		fields["error"] = err.Error()
		p.logger.WithContext(ctx).WithFields(fields).Error("Failed to send Kafka message")
		return fmt.Errorf("failed to send message to topic %s: %w", topic, err)
	}

	p.logger.WithContext(ctx).WithFields(fields).Debug("Kafka message sent successfully")
	return nil
}

func (p *Producer) Close() error {
	return p.writer.Close()
}

type EventPublisher struct {
	producer    *Producer
	logger      *logging.Logger
	config      *config.KafkaConfig
	serviceName string
	version     string
}

type BaseEvent struct {
	EventID     string    `json:"event_id"`
	EventType   string    `json:"event_type"`
	Timestamp   time.Time `json:"timestamp"`
	ServiceName string    `json:"service_name"`
	Version     string    `json:"version"`
}

func NewEventPublisher(cfg *config.KafkaConfig, logger *logging.Logger, serviceName, version string) *EventPublisher {
	producer := NewProducer(cfg, logger)
	return &EventPublisher{
		producer:    producer,
		logger:      logger,
		config:      cfg,
		serviceName: serviceName,
		version:     version,
	}
}

func (ep *EventPublisher) PublishEvent(ctx context.Context, topic string, eventType string, payload any, key string) error {
	eventID := fmt.Sprintf("%s-%d", eventType, time.Now().UnixNano())

	event := map[string]any{
		"event_id":     eventID,
		"event_type":   eventType,
		"timestamp":    time.Now(),
		"service_name": ep.serviceName,
		"version":      ep.version,
		"payload":      payload,
	}

	message := &Message{
		Key:   key,
		Value: event,
		Headers: map[string]string{
			"event-type":   eventType,
			"content-type": "application/json",
			"service-name": ep.serviceName,
			"event-id":     eventID,
		},
		Timestamp: time.Now(),
	}

	ep.logger.WithContext(ctx).WithFields(logrus.Fields{
		"topic":      topic,
		"event_type": eventType,
		"event_id":   eventID,
		"key":        key,
	}).Debug("Publishing event to Kafka")

	return ep.producer.SendMessage(ctx, topic, message)
}

func (ep *EventPublisher) PublishEventWithRetry(ctx context.Context, topic string, eventType string, payload any, key string, maxRetries int) error {
	var lastErr error

	for attempt := 1; attempt <= maxRetries; attempt++ {
		err := ep.PublishEvent(ctx, topic, eventType, payload, key)
		if err == nil {
			return nil
		}

		lastErr = err
		ep.logger.WithContext(ctx).WithFields(logrus.Fields{
			"attempt":     attempt,
			"max_retries": maxRetries,
			"error":       err.Error(),
			"topic":       topic,
			"event_type":  eventType,
		}).Warn("Failed to publish event, retrying...")

		if attempt < maxRetries {
			time.Sleep(time.Duration(attempt) * time.Second)
		}
	}

	return fmt.Errorf("failed to publish event after %d attempts: %w", maxRetries, lastErr)
}

func (ep *EventPublisher) Close() error {
	return ep.producer.Close()
}
