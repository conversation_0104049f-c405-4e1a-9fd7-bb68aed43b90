package kafka

import (
	"context"
	"time"

	"github.com/segmentio/kafka-go"
	"github.com/sirupsen/logrus"

	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
)

type Consumer struct {
	reader *kafka.Reader
	config *config.KafkaConfig
	logger *logging.Logger
}

type MessageHandler func(ctx context.Context, msg *kafka.Message) error

func NewConsumer(cfg *config.KafkaConfig, topic string, logger *logging.Logger) *Consumer {
	reader := kafka.NewReader(kafka.ReaderConfig{
		Brokers: cfg.Brokers,
		Topic:   topic,
		GroupID: cfg.GroupID,
	})

	return &Consumer{
		reader: reader,
		config: cfg,
		logger: logger,
	}
}

func (c *Consumer) Consume(ctx context.Context, handler MessageHandler) error {
	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
			msg, err := c.reader.ReadMessage(ctx)
			if err != nil {
				c.logger.WithContext(ctx).WithFields(logrus.Fields{
					"error": err.Error(),
				}).Error("Failed to read Kafka message")
				continue
			}

			start := time.Now()
			err = handler(ctx, &msg)
			duration := time.Since(start)

			fields := logrus.Fields{
				"topic":     msg.Topic,
				"partition": msg.Partition,
				"offset":    msg.Offset,
				"duration":  duration.String(),
			}

			if err != nil {
				fields["error"] = err.Error()
				c.logger.WithContext(ctx).WithFields(fields).Error("Failed to process Kafka message")
				continue
			}

			c.logger.WithContext(ctx).WithFields(fields).Debug("Kafka message processed successfully")
		}
	}
}

func (c *Consumer) Close() error {
	return c.reader.Close()
}
