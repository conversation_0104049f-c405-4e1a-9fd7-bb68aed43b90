package health

import (
	"context"
	"net/http"
	"time"

	"github.com/labstack/echo/v4"
)

type HealthCheck struct {
	Name  string
	Check func(ctx context.Context) error
}

type HealthChecker struct {
	checks []HealthCheck
}

type HealthStatus struct {
	Status    string                 `json:"status"`
	Checks    map[string]CheckStatus `json:"checks"`
	Timestamp time.Time              `json:"timestamp"`
}

type CheckStatus struct {
	Status  string `json:"status"`
	Message string `json:"message,omitempty"`
}

func NewHealthChecker() *HealthChecker {
	return &HealthChecker{
		checks: make([]HealthCheck, 0),
	}
}

func (h *HealthChecker) AddCheck(name string, check func(ctx context.Context) error) {
	h.checks = append(h.checks, HealthCheck{
		Name:  name,
		Check: check,
	})
}

func (h *HealthChecker) CheckHealth(ctx context.Context) HealthStatus {
	status := HealthStatus{
		Status:    "healthy",
		Checks:    make(map[string]CheckStatus),
		Timestamp: time.Now(),
	}

	for _, check := range h.checks {
		checkCtx, cancel := context.WithTimeout(ctx, 5*time.Second)

		if err := check.Check(checkCtx); err != nil {
			status.Status = "unhealthy"
			status.Checks[check.Name] = CheckStatus{
				Status:  "unhealthy",
				Message: err.Error(),
			}
		} else {
			status.Checks[check.Name] = CheckStatus{
				Status: "healthy",
			}
		}

		cancel()
	}

	return status
}

func (h *HealthChecker) HTTPHandler() echo.HandlerFunc {
	return func(c echo.Context) error {
		status := h.CheckHealth(c.Request().Context())

		httpStatus := http.StatusOK
		if status.Status == "unhealthy" {
			httpStatus = http.StatusServiceUnavailable
		}

		return c.JSON(httpStatus, status)
	}
}
