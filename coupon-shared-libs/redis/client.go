package redis

import (
	"context"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/sirupsen/logrus"

	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
)

type Client struct {
	client  *redis.Client
	config  *config.RedisConfig
	logger  *logging.Logger
	metrics *metrics.Metrics
}

func NewClient(cfg *config.RedisConfig, logger *logging.Logger, metrics *metrics.Metrics) *Client {
	rdb := redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%d", cfg.Host, cfg.Port),
		Password: cfg.Password,
		DB:       cfg.DB,
	})

	return &Client{
		client:  rdb,
		config:  cfg,
		logger:  logger,
		metrics: metrics,
	}
}

func (c *Client) Get(ctx context.Context, key string) (string, error) {
	start := time.Now()

	val, err := c.client.Get(ctx, key).Result()

	duration := time.Since(start)
	status := "hit"
	if err == redis.Nil {
		status = "miss"
		err = nil
	} else if err != nil {
		status = "error"
	}

	c.metrics.RecordCacheOperation("redis", "get", status)

	c.logger.WithContext(ctx).WithFields(logrus.Fields{
		"key":      key,
		"duration": duration.String(),
		"status":   status,
	}).Debug("Redis GET operation")

	return val, err
}

func (c *Client) Set(ctx context.Context, key string, value any, expiration time.Duration) error {
	start := time.Now()

	err := c.client.Set(ctx, key, value, expiration).Err()

	duration := time.Since(start)
	status := "success"
	if err != nil {
		status = "error"
	}

	c.metrics.RecordCacheOperation("redis", "set", status)

	c.logger.WithContext(ctx).WithFields(logrus.Fields{
		"key":        key,
		"duration":   duration.String(),
		"status":     status,
		"expiration": expiration.String(),
	}).Debug("Redis SET operation")

	return err
}

func (c *Client) Del(ctx context.Context, keys ...string) error {
	start := time.Now()

	err := c.client.Del(ctx, keys...).Err()

	duration := time.Since(start)
	status := "success"
	if err != nil {
		status = "error"
	}

	c.metrics.RecordCacheOperation("redis", "del", status)

	c.logger.WithContext(ctx).WithFields(logrus.Fields{
		"keys":     keys,
		"duration": duration.String(),
		"status":   status,
	}).Debug("Redis DEL operation")

	return err
}

func (c *Client) Exists(ctx context.Context, keys ...string) (int64, error) {
	start := time.Now()

	count, err := c.client.Exists(ctx, keys...).Result()

	duration := time.Since(start)
	status := "success"
	if err != nil {
		status = "error"
	}

	c.metrics.RecordCacheOperation("redis", "exists", status)

	c.logger.WithContext(ctx).WithFields(logrus.Fields{
		"keys":     keys,
		"count":    count,
		"duration": duration.String(),
		"status":   status,
	}).Debug("Redis EXISTS operation")

	return count, err
}

func (c *Client) Close() error {
	return c.client.Close()
}

func (c *Client) Health(ctx context.Context) error {
	healthCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	return c.client.Ping(healthCtx).Err()
}
