# Coupon Shared Libraries

This repository provides reusable Go packages for coupon microservices. The libraries cover configuration loading, database helpers, Kafka utilities, logging, metrics, tracing and more.

## Packages

- **auth** – JWT utilities for service authentication
- **config** – Service configuration loader using Viper
- **database** – PostgreSQL helpers built on GORM
- **errors** – gRPC error helpers
- **grpc** – Client and server setup wrappers
- **health** – Echo based health check handler
- **kafka** – Kafka consumer and producer utilities
- **logging** – Structured logger with tracing context
- **metrics** – Prometheus metrics collectors
- **redis** – Redis client wrapper with instrumentation
- **tracing** – Jaeger tracer initialization

## Getting Started

To include the libraries in your project:

```bash
go get gitlab.zalopay.vn/phunn4/coupon-shared-libs
```

All modules require Go 1.24 or newer.

## Development

The `Makefile` provides several helpful commands:

- `make build` – compile all packages
- `make test` – run unit tests
- `make lint` – execute `golangci-lint`
- `make clean` – remove build artifacts
- `make proto` – generate gRPC code from `.proto` files
- `make deps` – download Go module dependencies
- `make fmt` – format Go files with `gofmt`
- `make vet` – run `go vet` on all packages
- `make tidy` – tidy module dependencies

Run `make build` or other targets as needed during development.
