package tracing

import (
	"context"
	"fmt"
	"io"

	"github.com/opentracing/opentracing-go"
	"github.com/opentracing/opentracing-go/ext"
	"github.com/uber/jaeger-client-go"
	"github.com/uber/jaeger-client-go/config"
)

type Tracer struct {
	tracer opentracing.Tracer
	closer io.Closer
	name   string
}

func New(serviceName, jaegerHost string, jaegerPort int) (*Tracer, error) {
	cfg := config.Configuration{
		ServiceName: serviceName,
		Sampler: &config.SamplerConfig{
			Type:  jaeger.SamplerTypeConst,
			Param: 1,
		},
		Reporter: &config.ReporterConfig{
			LogSpans:           true,
			LocalAgentHostPort: fmt.Sprintf("%s:%d", jaegerHost, jaegerPort),
		},
	}

	tracer, closer, err := cfg.NewTracer()
	if err != nil {
		return nil, fmt.Errorf("failed to create tracer: %w", err)
	}

	opentracing.SetGlobalTracer(tracer)

	return &Tracer{
		tracer: tracer,
		closer: closer,
		name:   serviceName,
	}, nil
}

func (t *Tracer) Close() error {
	return t.closer.Close()
}

func (t *Tracer) StartSpan(ctx context.Context, operationName string) (opentracing.Span, context.Context) {
	span, ctx := opentracing.StartSpanFromContext(ctx, operationName)
	return span, ctx
}

func (t *Tracer) StartSpanWithTag(ctx context.Context, operationName string, tags map[string]any) (opentracing.Span, context.Context) {
	span, ctx := opentracing.StartSpanFromContext(ctx, operationName)
	for key, value := range tags {
		span.SetTag(key, value)
	}
	return span, ctx
}

func (t *Tracer) StartHTTPSpan(ctx context.Context, method, url string) (opentracing.Span, context.Context) {
	span, ctx := opentracing.StartSpanFromContext(ctx, fmt.Sprintf("%s %s", method, url))
	ext.HTTPMethod.Set(span, method)
	ext.HTTPUrl.Set(span, url)
	ext.Component.Set(span, "http")
	return span, ctx
}

func (t *Tracer) StartGRPCSpan(ctx context.Context, method string) (opentracing.Span, context.Context) {
	span, ctx := opentracing.StartSpanFromContext(ctx, method)
	ext.Component.Set(span, "grpc")
	return span, ctx
}

func FinishSpan(span opentracing.Span, err error) {
	if err != nil {
		ext.Error.Set(span, true)
		span.SetTag("error.message", err.Error())
	}
	span.Finish()
}
