package benchmark

import (
	"context"
	"fmt"
	"net/http"
	"runtime"
	"testing"
	"time"

	"gorm.io/gorm"
)

type BenchmarkWrapper struct {
	metrics     *BenchmarkMetrics
	serviceName string
	server      *http.Server
}

func NewBenchmarkWrapper(serviceName, pushGateway string, metricsPort int) *BenchmarkWrapper {
	metrics := NewBenchmarkMetrics(serviceName, pushGateway)

	var server *http.Server
	if metricsPort > 0 {
		server = metrics.StartMetricsServer(metricsPort)
	}

	return &BenchmarkWrapper{
		metrics:     metrics,
		serviceName: serviceName,
		server:      server,
	}
}

func (bw *BenchmarkWrapper) RunBenchmarkWithMetrics(b *testing.B, benchmarkName, operationType string, benchmarkFunc func(b *testing.B)) {
	setupStart := time.Now()

	defer func() {
		setupDuration := time.Since(setupStart)
		bw.metrics.RecordSetupDuration(benchmarkName, setupDuration)
	}()

	var memStatsBefore runtime.MemStats
	runtime.GC()
	runtime.ReadMemStats(&memStatsBefore)

	testStart := time.Now()

	benchmarkFunc(b)

	testDuration := time.Since(testStart)

	var memStatsAfter runtime.MemStats
	runtime.GC()
	runtime.ReadMemStats(&memStatsAfter)

	result := &BenchmarkResult{
		TestName:      benchmarkName,
		OperationType: operationType,
		N:             b.N,
		NsPerOp:       b.Elapsed().Nanoseconds() / int64(b.N),
		Duration:      testDuration,
		OpsPerSecond:  float64(b.N) / testDuration.Seconds(),
		TotalOps:      int64(b.N),
		Timestamp:     time.Now(),
	}

	if b.N > 0 {
		result.AllocsPerOp = int64(memStatsAfter.Mallocs-memStatsBefore.Mallocs) / int64(b.N)
		result.BytesPerOp = int64(memStatsAfter.TotalAlloc-memStatsBefore.TotalAlloc) / int64(b.N)
		result.MemAllocsPerOp = result.AllocsPerOp
	}

	bw.recordBenchmarkMetrics(result)

	bw.metrics.RecordTestDuration(benchmarkName, testDuration)

	if err := bw.metrics.PushMetrics(); err != nil {
		b.Logf("Failed to push metrics: %v", err)
	}
}

func (bw *BenchmarkWrapper) RunDatabaseBenchmarkWithMetrics(b *testing.B, benchmarkName, queryType, table string, db *gorm.DB, benchmarkFunc func(b *testing.B, db *gorm.DB)) {
	setupStart := time.Now()

	sqlDB, err := db.DB()
	if err != nil {
		b.Fatalf("Failed to get underlying sql.DB: %v", err)
	}

	_ = sqlDB.Stats()

	bw.metrics.RecordSetupDuration(benchmarkName, time.Since(setupStart))

	testStart := time.Now()

	b.ResetTimer()
	for i := 0; b.Loop(); i++ {
		queryStart := time.Now()

		b.Run(fmt.Sprintf("query_%d", i), func(subB *testing.B) {
			benchmarkFunc(subB, db)
		})

		queryDuration := time.Since(queryStart)
		bw.metrics.RecordDatabaseQuery(benchmarkName, queryType, table, queryDuration)
	}
	b.StopTimer()

	testDuration := time.Since(testStart)

	finalStats := sqlDB.Stats()

	bw.metrics.RecordDatabaseConnections(benchmarkName, finalStats.OpenConnections)

	opsPerSec := float64(b.N) / testDuration.Seconds()
	bw.metrics.RecordOperationsPerSecond(benchmarkName, queryType, opsPerSec)

	bw.metrics.RecordTestDuration(benchmarkName, testDuration)

	b.Logf("Database Stats - Open: %d, InUse: %d, Idle: %d",
		finalStats.OpenConnections, finalStats.InUse, finalStats.Idle)
	b.Logf("Query Performance - %s on %s: %.2f ops/sec", queryType, table, opsPerSec)

	if err := bw.metrics.PushMetrics(); err != nil {
		b.Logf("Failed to push metrics: %v", err)
	}
}

func (bw *BenchmarkWrapper) RunGRPCBenchmarkWithMetrics(b *testing.B, benchmarkName, method string, benchmarkFunc func(b *testing.B) error) {
	setupStart := time.Now()
	bw.metrics.RecordSetupDuration(benchmarkName, time.Since(setupStart))

	var successCount, errorCount int
	testStart := time.Now()

	b.ResetTimer()
	for i := 0; b.Loop(); i++ {
		requestStart := time.Now()

		err := benchmarkFunc(b)

		requestDuration := time.Since(requestStart)
		status := "success"
		if err != nil {
			status = "error"
			errorCount++
		} else {
			successCount++
		}

		bw.metrics.RecordGRPCRequest(benchmarkName, method, status, requestDuration)
	}
	b.StopTimer()

	testDuration := time.Since(testStart)

	opsPerSec := float64(b.N) / testDuration.Seconds()
	errorRate := float64(errorCount) / float64(b.N) * 100

	bw.metrics.RecordOperationsPerSecond(benchmarkName, "grpc_request", opsPerSec)
	bw.metrics.RecordErrorRate(benchmarkName, "grpc_error", errorRate)
	bw.metrics.RecordTestDuration(benchmarkName, testDuration)

	b.Logf("gRPC Performance - %s: %.2f ops/sec, %.2f%% error rate", method, opsPerSec, errorRate)

	if err := bw.metrics.PushMetrics(); err != nil {
		b.Logf("Failed to push metrics: %v", err)
	}
}

func (bw *BenchmarkWrapper) RunLoadTestWithMetrics(b *testing.B, benchmarkName string, concurrentUsers int, duration time.Duration, testFunc func() error) {
	setupStart := time.Now()
	bw.metrics.RecordSetupDuration(benchmarkName, time.Since(setupStart))
	bw.metrics.RecordConcurrentUsers(benchmarkName, concurrentUsers)

	results := make(chan error, concurrentUsers*100)
	done := make(chan bool)

	testStart := time.Now()

	for i := range concurrentUsers {
		go func(workerID int) {
			for {
				select {
				case <-done:
					return
				default:
					requestStart := time.Now()
					err := testFunc()
					requestDuration := time.Since(requestStart)

					bw.metrics.RecordLatency(benchmarkName, "load_test", requestDuration)
					results <- err
				}
			}
		}(i)
	}

	time.Sleep(duration)
	close(done)

	testDuration := time.Since(testStart)

	var totalRequests, errorCount int
	timeout := time.After(1 * time.Second)

collectLoop:
	for {
		select {
		case err := <-results:
			totalRequests++
			if err != nil {
				errorCount++
			}
		case <-timeout:
			break collectLoop
		}
	}

	opsPerSec := float64(totalRequests) / testDuration.Seconds()
	errorRate := float64(errorCount) / float64(totalRequests) * 100

	bw.metrics.RecordOperationsPerSecond(benchmarkName, "load_test", opsPerSec)
	bw.metrics.RecordErrorRate(benchmarkName, "load_test_error", errorRate)
	bw.metrics.RecordTestDuration(benchmarkName, testDuration)

	b.Logf("Load Test Results - Users: %d, Requests: %d, OPS: %.2f, Error Rate: %.2f%%",
		concurrentUsers, totalRequests, opsPerSec, errorRate)

	if err := bw.metrics.PushMetrics(); err != nil {
		b.Logf("Failed to push metrics: %v", err)
	}
}

func (bw *BenchmarkWrapper) RecordCleanupMetrics(benchmarkName string, cleanupFunc func() error) error {
	cleanupStart := time.Now()
	err := cleanupFunc()
	cleanupDuration := time.Since(cleanupStart)

	bw.metrics.RecordCleanupDuration(benchmarkName, cleanupDuration)

	if err != nil {
		bw.metrics.RecordErrorRate(benchmarkName, "cleanup_error", 100.0)
	} else {
		bw.metrics.RecordErrorRate(benchmarkName, "cleanup_error", 0.0)
	}

	if pushErr := bw.metrics.PushMetrics(); pushErr != nil {
		fmt.Printf("Failed to push cleanup metrics: %v\n", pushErr)
	}

	return err
}

func (bw *BenchmarkWrapper) Shutdown(ctx context.Context) error {
	return bw.metrics.Shutdown(ctx, bw.server)
}

func (bw *BenchmarkWrapper) recordBenchmarkMetrics(result *BenchmarkResult) {
	bw.metrics.RecordOperationsPerSecond(result.TestName, result.OperationType, result.OpsPerSecond)
	bw.metrics.RecordMemoryAllocations(result.TestName, result.OperationType, float64(result.MemAllocsPerOp))

	avgLatency := time.Duration(result.NsPerOp) * time.Nanosecond
	bw.metrics.RecordLatency(result.TestName, result.OperationType, avgLatency)
}
