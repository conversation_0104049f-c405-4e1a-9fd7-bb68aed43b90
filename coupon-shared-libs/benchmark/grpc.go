package benchmark

import (
	"context"
	"fmt"
	"sync"
	"testing"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

type GRPCBenchmarkConfig struct {
	ServiceAddress string
	ServiceName    string
	MethodName     string
	Concurrency    int
	RequestSize    int
	Timeout        time.Duration
}

type GRPCBenchmarkSuite struct {
	config *GRPCBenchmarkConfig
	Conn   *grpc.ClientConn
	mu     sync.RWMutex
}

func NewGRPCBenchmarkSuite(config *GRPCBenchmarkConfig) (*GRPCBenchmarkSuite, error) {
	conn, err := grpc.NewClient(config.ServiceAddress, grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		return nil, fmt.Errorf("failed to connect to gRPC service: %w", err)
	}

	return &GRPCBenchmarkSuite{
		config: config,
		Conn:   conn,
	}, nil
}

func (gbs *GRPCBenchmarkSuite) Close() error {
	return gbs.Conn.Close()
}

func (gbs *GRPCBenchmarkSuite) BenchmarkUnaryCall(
	b *testing.B,
	callFunc func(ctx context.Context, conn *grpc.ClientConn) error,
) {
	framework := NewBenchmarkFramework(&BenchmarkConfig{
		ServiceName:    gbs.config.ServiceName,
		TestName:       fmt.Sprintf("grpc_%s", gbs.config.MethodName),
		MetricsEnabled: true,
	})

	operation := func(ctx context.Context) error {
		ctx, cancel := context.WithTimeout(ctx, gbs.config.Timeout)
		defer cancel()
		return callFunc(ctx, gbs.Conn)
	}

	framework.RunBenchmark(b, operation)
}

func (gbs *GRPCBenchmarkSuite) BenchmarkStreamingCall(
	b *testing.B,
	streamFunc func(ctx context.Context, conn *grpc.ClientConn) error,
) {
	framework := NewBenchmarkFramework(&BenchmarkConfig{
		ServiceName:    gbs.config.ServiceName,
		TestName:       fmt.Sprintf("grpc_stream_%s", gbs.config.MethodName),
		MetricsEnabled: true,
	})

	operation := func(ctx context.Context) error {
		ctx, cancel := context.WithTimeout(ctx, gbs.config.Timeout)
		defer cancel()
		return streamFunc(ctx, gbs.Conn)
	}

	framework.RunBenchmark(b, operation)
}

func (gbs *GRPCBenchmarkSuite) BenchmarkConcurrentCalls(
	b *testing.B,
	callFunc func(ctx context.Context, conn *grpc.ClientConn) error,
	concurrency int,
) {
	framework := NewBenchmarkFramework(&BenchmarkConfig{
		ServiceName:    gbs.config.ServiceName,
		TestName:       fmt.Sprintf("grpc_concurrent_%s", gbs.config.MethodName),
		Concurrency:    concurrency,
		MetricsEnabled: true,
	})

	operation := func(ctx context.Context) error {
		ctx, cancel := context.WithTimeout(ctx, gbs.config.Timeout)
		defer cancel()
		return callFunc(ctx, gbs.Conn)
	}

	framework.RunBenchmark(b, operation)
}

type AuthBenchmarkHelper struct {
	clientID  string
	clientKey string
}

func NewAuthBenchmarkHelper(clientID, clientKey string) *AuthBenchmarkHelper {
	return &AuthBenchmarkHelper{
		clientID:  clientID,
		clientKey: clientKey,
	}
}

func (abh *AuthBenchmarkHelper) CreateAuthContext(ctx context.Context) context.Context {
	return ctx
}

type LoadTestScenario struct {
	Name        string
	Duration    time.Duration
	Concurrency int
	RampUpTime  time.Duration
	Operations  []LoadTestOperation
}

type LoadTestOperation struct {
	Name     string
	Weight   int
	Execute  func(ctx context.Context) error
	Validate func(result any) error
}

type LoadTestRunner struct {
	scenario *LoadTestScenario
	results  []BenchmarkResult
	mu       sync.RWMutex
}

func NewLoadTestRunner(scenario *LoadTestScenario) *LoadTestRunner {
	return &LoadTestRunner{
		scenario: scenario,
		results:  make([]BenchmarkResult, 0),
	}
}

func (ltr *LoadTestRunner) RunLoadTest(b *testing.B) {
	b.Logf("Starting load test: %s", ltr.scenario.Name)
	b.Logf("Duration: %v, Concurrency: %d, RampUp: %v",
		ltr.scenario.Duration, ltr.scenario.Concurrency, ltr.scenario.RampUpTime)

	ctx, cancel := context.WithTimeout(context.Background(), ltr.scenario.Duration)
	defer cancel()

	var wg sync.WaitGroup

	rampUpInterval := ltr.scenario.RampUpTime / time.Duration(ltr.scenario.Concurrency)

	for i := 0; i < ltr.scenario.Concurrency; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()

			time.Sleep(time.Duration(workerID) * rampUpInterval)

			ltr.runWorker(ctx)
		}(i)
	}

	wg.Wait()

	ltr.reportLoadTestResults(b)
}

func (ltr *LoadTestRunner) runWorker(ctx context.Context) {
	generator := NewTestDataGenerator()

	for {
		select {
		case <-ctx.Done():
			return
		default:
			operation := ltr.selectOperation(generator)

			start := time.Now()
			err := operation.Execute(ctx)
			duration := time.Since(start)

			result := BenchmarkResult{
				ServiceName:  ltr.scenario.Name,
				TestName:     operation.Name,
				TotalOps:     1,
				Duration:     duration,
				OpsPerSecond: 1.0 / duration.Seconds(),
				AvgLatency:   duration,
				ErrorRate:    0,
				Timestamp:    time.Now(),
			}

			if err != nil {
				result.ErrorRate = 100
			}

			ltr.recordResult(result)
		}
	}
}

func (ltr *LoadTestRunner) selectOperation(generator *TestDataGenerator) LoadTestOperation {
	totalWeight := 0
	for _, op := range ltr.scenario.Operations {
		totalWeight += op.Weight
	}

	random := generator.rand.Intn(totalWeight)
	currentWeight := 0

	for _, op := range ltr.scenario.Operations {
		currentWeight += op.Weight
		if random < currentWeight {
			return op
		}
	}

	return ltr.scenario.Operations[0]
}

func (ltr *LoadTestRunner) recordResult(result BenchmarkResult) {
	ltr.mu.Lock()
	defer ltr.mu.Unlock()
	ltr.results = append(ltr.results, result)
}

func (ltr *LoadTestRunner) reportLoadTestResults(b *testing.B) {
	ltr.mu.RLock()
	defer ltr.mu.RUnlock()

	if len(ltr.results) == 0 {
		b.Log("No results to report")
		return
	}

	operationStats := make(map[string]struct {
		count     int
		totalOps  int64
		totalTime time.Duration
		errors    int
	})

	for _, result := range ltr.results {
		stats := operationStats[result.TestName]
		stats.count++
		stats.totalOps += result.TotalOps
		stats.totalTime += result.Duration
		if result.ErrorRate > 0 {
			stats.errors++
		}
		operationStats[result.TestName] = stats
	}

	b.Logf("Load Test Results for %s:", ltr.scenario.Name)
	for opName, stats := range operationStats {
		avgLatency := stats.totalTime / time.Duration(stats.count)
		errorRate := float64(stats.errors) / float64(stats.count) * 100
		opsPerSec := float64(stats.totalOps) / stats.totalTime.Seconds()

		b.Logf("  %s:", opName)
		b.Logf("    Operations: %d", stats.totalOps)
		b.Logf("    Avg Latency: %v", avgLatency)
		b.Logf("    Ops/sec: %.2f", opsPerSec)
		b.Logf("    Error Rate: %.2f%%", errorRate)
	}
}
