package service

import (
	"context"

	"gitlab.zalopay.vn/phunn4/coupon-auth-service/internal/model"
	"gitlab.zalopay.vn/phunn4/coupon-auth-service/internal/repository"
	proto_auth_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/auth/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/errors"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"golang.org/x/crypto/bcrypt"
)

type AuthService interface {
	RegisterService(ctx context.Context, req *proto_auth_v1.RegisterServiceRequest) (*proto_auth_v1.RegisterServiceResponse, error)
	ValidateServiceCredentials(ctx context.Context, req *proto_auth_v1.ValidateServiceCredentialsRequest) (*proto_auth_v1.ValidateServiceCredentialsResponse, error)
}

type authService struct {
	repo   repository.AuthRepository
	logger *logging.Logger
	cfg    *config.Config
}

func NewAuthService(repo repository.AuthRepository, logger *logging.Logger, cfg *config.Config) AuthService {
	return &authService{
		repo:   repo,
		logger: logger,
		cfg:    cfg,
	}
}

func (s *authService) RegisterService(ctx context.Context, req *proto_auth_v1.RegisterServiceRequest) (*proto_auth_v1.RegisterServiceResponse, error) {
	cred, rawKey, err := model.NewServiceCredential(req.ServiceName, req.ServiceVersion)
	if err != nil {
		return nil, errors.NewInternalError("could not generate credentials")
	}

	if err := s.repo.RegisterService(ctx, cred); err != nil {
		return nil, errors.NewInternalError("failed to save service credentials")
	}

	return &proto_auth_v1.RegisterServiceResponse{
		ServiceId: cred.ID,
		ClientId:  cred.ClientID,
		ClientKey: rawKey,
	}, nil
}

func (s *authService) ValidateServiceCredentials(ctx context.Context, req *proto_auth_v1.ValidateServiceCredentialsRequest) (*proto_auth_v1.ValidateServiceCredentialsResponse, error) {
	cred, err := s.repo.GetServiceByClientID(ctx, req.ClientId)
	if err != nil {
		return nil, errors.NewUnauthorizedError("invalid client id or key")
	}

	if bcrypt.CompareHashAndPassword([]byte(cred.ClientKey), []byte(req.ClientKey)) != nil {
		return nil, errors.NewUnauthorizedError("invalid client id or key")
	}

	return &proto_auth_v1.ValidateServiceCredentialsResponse{
		Valid:       true,
		ServiceId:   cred.ID,
		ServiceName: cred.Name,
	}, nil
}
