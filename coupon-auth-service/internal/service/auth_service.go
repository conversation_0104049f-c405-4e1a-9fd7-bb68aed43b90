package service

import (
	"context"
	"time"

	"gitlab.zalopay.vn/phunn4/coupon-auth-service/internal/model"
	"gitlab.zalopay.vn/phunn4/coupon-auth-service/internal/repository"
	proto_auth_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/auth/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/errors"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
	"golang.org/x/crypto/bcrypt"
)

type AuthService interface {
	RegisterService(ctx context.Context, req *proto_auth_v1.RegisterServiceRequest) (*proto_auth_v1.RegisterServiceResponse, error)
	ValidateServiceCredentials(ctx context.Context, req *proto_auth_v1.ValidateServiceCredentialsRequest) (*proto_auth_v1.ValidateServiceCredentialsResponse, error)
}

type authService struct {
	repo            repository.AuthRepository
	logger          *logging.Logger
	cfg             *config.Config
	businessMetrics *metrics.BusinessMetrics
}

func NewAuthService(repo repository.AuthRepository, logger *logging.Logger, cfg *config.Config, businessMetrics *metrics.BusinessMetrics) AuthService {
	return &authService{
		repo:            repo,
		logger:          logger,
		cfg:             cfg,
		businessMetrics: businessMetrics,
	}
}

func (s *authService) RegisterService(ctx context.Context, req *proto_auth_v1.RegisterServiceRequest) (*proto_auth_v1.RegisterServiceResponse, error) {
	start := time.Now()
	
	cred, rawKey, err := model.NewServiceCredential(req.ServiceName, req.ServiceVersion)
	if err != nil {
		s.businessMetrics.RecordServiceAuthentication("error", time.Since(start))
		return nil, errors.NewInternalError("could not generate credentials")
	}

	if err := s.repo.RegisterService(ctx, cred); err != nil {
		s.businessMetrics.RecordServiceAuthentication("error", time.Since(start))
		return nil, errors.NewInternalError("failed to save service credentials")
	}

	s.businessMetrics.RecordServiceAuthentication("success", time.Since(start))
	return &proto_auth_v1.RegisterServiceResponse{
		ServiceId: cred.ID,
		ClientId:  cred.ClientID,
		ClientKey: rawKey,
	}, nil
}

func (s *authService) ValidateServiceCredentials(ctx context.Context, req *proto_auth_v1.ValidateServiceCredentialsRequest) (*proto_auth_v1.ValidateServiceCredentialsResponse, error) {
	start := time.Now()
	
	cred, err := s.repo.GetServiceByClientID(ctx, req.ClientId)
	if err != nil {
		s.businessMetrics.RecordTokenValidation("error", time.Since(start))
		return nil, errors.NewUnauthorizedError("invalid client id or key")
	}

	if bcrypt.CompareHashAndPassword([]byte(cred.ClientKey), []byte(req.ClientKey)) != nil {
		s.businessMetrics.RecordTokenValidation("error", time.Since(start))
		return nil, errors.NewUnauthorizedError("invalid client id or key")
	}

	s.businessMetrics.RecordTokenValidation("success", time.Since(start))
	return &proto_auth_v1.ValidateServiceCredentialsResponse{
		Valid:       true,
		ServiceId:   cred.ID,
		ServiceName: cred.Name,
	}, nil
}
