package handler

import (
	"context"

	"gitlab.zalopay.vn/phunn4/coupon-auth-service/internal/service"
	proto_auth_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/auth/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/errors"
)

type AuthServer struct {
	proto_auth_v1.UnimplementedAuthServiceServer
	svc service.AuthService
}

func NewAuthServer(svc service.AuthService) *AuthServer {
	return &AuthServer{svc: svc}
}

func (s *AuthServer) RegisterService(ctx context.Context, req *proto_auth_v1.RegisterServiceRequest) (*proto_auth_v1.RegisterServiceResponse, error) {
	resp, err := s.svc.RegisterService(ctx, req)
	if err != nil {
		return nil, errors.ToGRPCError(err)
	}
	return resp, nil
}

func (s *AuthServer) ValidateServiceCredentials(ctx context.Context, req *proto_auth_v1.ValidateServiceCredentialsRequest) (*proto_auth_v1.ValidateServiceCredentialsResponse, error) {
	resp, err := s.svc.ValidateServiceCredentials(ctx, req)
	if err != nil {
		return nil, errors.ToGRPCError(err)
	}
	return resp, nil
}
