package benchmark

import (
	"context"
	"fmt"
	"os"
	"testing"
	"time"

	"golang.org/x/crypto/bcrypt"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"gorm.io/gorm"

	proto_auth_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/auth/v1"
	proto_common_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/common/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/benchmark"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/database"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
)

var (
	grpcSuite        *benchmark.GRPCBenchmarkSuite
	dbSuite          *benchmark.DatabaseBenchmarkSuite
	testDB           *database.DB
	generator        *benchmark.TestDataGenerator
	benchmarkWrapper *benchmark.BenchmarkWrapper
	cfg              *config.Config
)

func TestMain(m *testing.M) {
	if err := setupAuthBenchmarkEnvironment(); err != nil {
		fmt.Printf("Failed to setup auth benchmark environment: %v\n", err)
		os.Exit(1)
	}

	code := m.Run()
	cleanupAuth()
	os.Exit(code)
}

func setupAuthBenchmarkEnvironment() error {
	cfg, err := config.LoadBenchmarkConfig()
	if err != nil {
		return fmt.Errorf("failed to load benchmark config: %w", err)
	}

	if cfg.BenchmarkMetrics.Enabled {
		benchmarkWrapper = benchmark.NewBenchmarkWrapper(
			cfg.Service.Name,
			cfg.BenchmarkMetrics.PushGateway,
			cfg.BenchmarkMetrics.MetricsPort,
		)
		fmt.Printf("✓ Benchmark metrics enabled - Push Gateway: %s, Metrics Port: %d\n",
			cfg.BenchmarkMetrics.PushGateway, cfg.BenchmarkMetrics.MetricsPort)
	}

	logger := logging.New("info", "json")
	appMetrics := metrics.New(cfg.Service.Name)

	testDB, err = database.NewPostgresDB(&cfg.Database, logger, appMetrics)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}

	if err := setupBenchmarkTables(); err != nil {
		return fmt.Errorf("failed to setup benchmark tables: %w", err)
	}

	grpcConfig := &benchmark.GRPCBenchmarkConfig{
		ServiceAddress: "0.0.0.0:50051",
		ServiceName:    cfg.Service.Name,
		Timeout:        5 * time.Second,
	}

	grpcSuite, err = benchmark.NewGRPCBenchmarkSuite(grpcConfig)
	if err != nil {
		return fmt.Errorf("failed to create gRPC benchmark suite: %w", err)
	}

	dbConfig := &benchmark.DatabaseBenchmarkConfig{
		ServiceName:    cfg.Service.Name,
		DatabaseType:   "postgres",
		ConnectionPool: 20,
		QueryTimeout:   5 * time.Second,
	}
	dbSuite = benchmark.NewDatabaseBenchmarkSuite(dbConfig, testDB.DB)

	generator = benchmark.NewTestDataGenerator()

	if err := setupBenchmarkTestData(); err != nil {
		return fmt.Errorf("failed to setup benchmark test data: %w", err)
	}

	return nil
}

func setupBenchmarkTables() error {
	models := []any{
		&BenchmarkServiceCredential{},
	}

	if err := testDB.AutoMigrate(models...); err != nil {
		return fmt.Errorf("failed to auto-migrate benchmark tables: %w", err)
	}

	return nil
}

func setupBenchmarkTestData() error {
	hashedKey, err := bcrypt.GenerateFromPassword([]byte("123456789"), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("failed to hash test key: %w", err)
	}

	testCredential := &BenchmarkServiceCredential{
		Name:      "test-service",
		ClientID:  "test-client-id",
		ClientKey: string(hashedKey),
		Version:   "1.0.0",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	var existingCredential BenchmarkServiceCredential
	result := testDB.Where("client_id = ?", testCredential.ClientID).First(&existingCredential)
	if result.Error != nil {
		if err := testDB.Create(testCredential).Error; err != nil {
			return fmt.Errorf("failed to create test service credential: %w", err)
		}
		fmt.Printf("✓ Created test credential with client_id: %s\n", testCredential.ClientID)
	} else {
		fmt.Printf("✓ Test credential already exists with client_id: %s\n", testCredential.ClientID)
	}

	return nil
}

func cleanupAuth() {
	if benchmarkWrapper != nil {
		benchmarkWrapper.RecordCleanupMetrics("auth_service_cleanup", func() error {
			if testDB != nil {
				result := testDB.Exec("DELETE FROM service_credentials_benchmark_test")
				if result.Error != nil {
					return result.Error
				}
				fmt.Printf("✓ Cleaned up %d benchmark service credential records\n", result.RowsAffected)
			}
			return nil
		})

		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()
		benchmarkWrapper.Shutdown(ctx)
	}

	if grpcSuite != nil {
		grpcSuite.Close()
	}
	if testDB != nil {
		sqlDB, _ := testDB.DB.DB()
		sqlDB.Close()
	}
}

func BenchmarkAuthService_gRPC_ValidateServiceCredentials(b *testing.B) {
	if benchmarkWrapper != nil {
		benchmarkWrapper.RunGRPCBenchmarkWithMetrics(b, "validate_service_credentials", "ValidateServiceCredentials", func(b *testing.B) error {
			conn, err := grpc.NewClient("localhost:50051", grpc.WithTransportCredentials(insecure.NewCredentials()))
			if err != nil {
				return err
			}
			defer conn.Close()

			client := proto_auth_v1.NewAuthServiceClient(conn)
			req := &proto_auth_v1.ValidateServiceCredentialsRequest{
				ClientId:    "test-client-id",
				ClientKey:   "123456789",
				ServiceName: "test-service",
			}

			_, err = client.ValidateServiceCredentials(context.Background(), req)
			return err
		})
	} else {
		grpcSuite.BenchmarkUnaryCall(b, func(ctx context.Context, conn *grpc.ClientConn) error {
			client := proto_auth_v1.NewAuthServiceClient(conn)

			req := &proto_auth_v1.ValidateServiceCredentialsRequest{
				ClientId:    "test-client-id",
				ClientKey:   "123456789",
				ServiceName: "test-service",
			}
			_, err := client.ValidateServiceCredentials(ctx, req)
			return err
		})
	}
}

func BenchmarkAuthService_gRPC_RegisterService(b *testing.B) {
	if benchmarkWrapper != nil {
		benchmarkWrapper.RunGRPCBenchmarkWithMetrics(b, "register_service", "RegisterService", func(b *testing.B) error {
			conn, err := grpc.NewClient("0.0.0.0:50051", grpc.WithTransportCredentials(insecure.NewCredentials()))
			if err != nil {
				return err
			}
			defer conn.Close()

			client := proto_auth_v1.NewAuthServiceClient(conn)

			req := &proto_auth_v1.RegisterServiceRequest{
				ServiceName:    fmt.Sprintf("test-service-%d", generator.GenerateUserID()),
				ServiceVersion: "1.0.0",
				Description:    "Test service for benchmarking",
			}

			_, err = client.RegisterService(context.Background(), req)
			return err
		})
	} else {
		grpcSuite.BenchmarkUnaryCall(b, func(ctx context.Context, conn *grpc.ClientConn) error {
			client := proto_auth_v1.NewAuthServiceClient(conn)

			req := &proto_auth_v1.RegisterServiceRequest{
				ServiceName:    fmt.Sprintf("test-service-%d", generator.GenerateUserID()),
				ServiceVersion: "1.0.0",
				Description:    "Test service for benchmarking",
			}

			_, err := client.RegisterService(ctx, req)
			return err
		})
	}
}

func BenchmarkAuthService_Database_ValidateServiceCredentials(b *testing.B) {
	dbSuite.BenchmarkQuery(b, "validate_service_credentials", func(ctx context.Context, db *gorm.DB) error {
		var credential BenchmarkServiceCredential

		return db.Where("client_id = ?", "test-client-id").
			First(&credential).Error
	})
}

func BenchmarkAuthService_Database_CreateServiceCredential(b *testing.B) {
	dbSuite.BenchmarkQuery(b, "create_service_credential", func(ctx context.Context, db *gorm.DB) error {
		credential := &BenchmarkServiceCredential{
			Name:      fmt.Sprintf("service_%d", generator.GenerateUserID()),
			ClientID:  fmt.Sprintf("client_%d", generator.GenerateUserID()),
			ClientKey: fmt.Sprintf("key_%d", generator.GenerateUserID()),
			Version:   "1.0.0",
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		return db.Create(credential).Error
	})
}

func BenchmarkAuthService_ConcurrentLoad(b *testing.B) {
	scenario := &benchmark.LoadTestScenario{
		Name:        "auth-service-mixed-load",
		Duration:    30 * time.Second,
		Concurrency: 20,
		RampUpTime:  5 * time.Second,
		Operations: []benchmark.LoadTestOperation{
			{
				Name:   "validate_service_credentials",
				Weight: 50,
				Execute: func(ctx context.Context) error {
					conn, err := grpc.NewClient("localhost:50051", grpc.WithTransportCredentials(insecure.NewCredentials()))
					if err != nil {
						return err
					}
					defer conn.Close()

					client := proto_auth_v1.NewAuthServiceClient(conn)
					req := &proto_auth_v1.ValidateServiceCredentialsRequest{
						ClientId:    "test-client-id",
						ClientKey:   "123456789",
						ServiceName: "test-service",
					}
					_, err = client.ValidateServiceCredentials(ctx, req)
					return err
				},
			},
			{
				Name:   "register_service",
				Weight: 30,
				Execute: func(ctx context.Context) error {
					conn, err := grpc.NewClient("localhost:50051", grpc.WithTransportCredentials(insecure.NewCredentials()))
					if err != nil {
						return err
					}
					defer conn.Close()

					client := proto_auth_v1.NewAuthServiceClient(conn)
					req := &proto_auth_v1.RegisterServiceRequest{
						ServiceName:    fmt.Sprintf("service-%d", generator.GenerateUserID()),
						ServiceVersion: "1.0.0",
						Description:    "Benchmark test service",
					}
					_, err = client.RegisterService(ctx, req)
					return err
				},
			},
			{
				Name:   "health_check",
				Weight: 20,
				Execute: func(ctx context.Context) error {
					conn, err := grpc.NewClient("localhost:50051", grpc.WithTransportCredentials(insecure.NewCredentials()))
					if err != nil {
						return err
					}
					defer conn.Close()

					client := proto_auth_v1.NewAuthServiceClient(conn)
					req := &proto_common_v1.HealthCheckRequest{}
					_, err = client.HealthCheck(ctx, req)
					return err
				},
			},
		},
	}

	runner := benchmark.NewLoadTestRunner(scenario)
	runner.RunLoadTest(b)
}
