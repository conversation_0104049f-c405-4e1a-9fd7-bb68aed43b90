package benchmark

import (
	"crypto/rand"
	"encoding/hex"
	"time"

	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"
)

type BenchmarkServiceCredential struct {
	ID        uint64    `gorm:"primaryKey;autoIncrement"`
	Name      string    `gorm:"type:varchar(255);not null;unique"`
	ClientID  string    `gorm:"type:varchar(255);not null;unique"`
	ClientKey string    `gorm:"not null"`
	Version   string    `gorm:"type:varchar(50)"`
	CreatedAt time.Time `gorm:"not null"`
	UpdatedAt time.Time `gorm:"not null"`
}

func (BenchmarkServiceCredential) TableName() string {
	return "service_credentials_benchmark_test"
}

func NewBenchmarkServiceCredential(name, version string) (*BenchmarkServiceCredential, string, error) {
	rawKey, err := generateRandomKey(32)
	if err != nil {
		return nil, "", err
	}

	keyHash, err := hashKey(rawKey)
	if err != nil {
		return nil, "", err
	}

	return &BenchmarkServiceCredential{
		Name:      name,
		Version:   version,
		ClientID:  uuid.NewString(),
		ClientKey: keyHash,
	}, rawKey, nil
}

func generateRandomKey(length int) (string, error) {
	bytes := make([]byte, length)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

func hashKey(key string) (string, error) {
	bytes, err := bcrypt.GenerateFromPassword([]byte(key), bcrypt.DefaultCost)
	return string(bytes), err
}
