groups:
  - name: authentication_alerts
    rules:
      - alert: HighAuthenticationFailureRate
        expr: |
          (
            sum(rate(auth_attempts_total{status!="success"}[5m])) by (service) /
            sum(rate(auth_attempts_total[5m])) by (service)
          ) * 100 > 10
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High authentication failure rate for {{ $labels.service }}"
          description: "{{ $labels.service }} has authentication failure rate of {{ $value }}%."

      - alert: CriticalAuthenticationFailureRate
        expr: |
          (
            sum(rate(auth_attempts_total{status!="success"}[5m])) by (service) /
            sum(rate(auth_attempts_total[5m])) by (service)
          ) * 100 > 25
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Critical authentication failure rate for {{ $labels.service }}"
          description: "{{ $labels.service }} has authentication failure rate of {{ $value }}%. Possible security incident."

      - alert: SuspiciousAuthenticationActivity
        expr: |
          sum(rate(auth_attempts_total{status!="success"}[1m])) by (service) > 10
        for: 30s
        labels:
          severity: critical
        annotations:
          summary: "Suspicious authentication activity detected"
          description: "{{ $labels.service }} is experiencing {{ $value }} failed authentication attempts per second. Possible brute force attack."

      - alert: HighJWTValidationFailures
        expr: |
          sum(rate(business_operations_total{operation="token_validation",status!="success"}[5m])) > 1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High JWT validation failures"
          description: "JWT validation failures at {{ $value }} per second. Check token expiration or signing issues."

      - alert: ServiceAuthenticationFailures
        expr: |
          sum(rate(business_operations_total{operation="service_authentication",status!="success"}[5m])) > 0.5
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Service-to-service authentication failures"
          description: "Service authentication failures at {{ $value }} per second. Check client credentials."

  - name: security_alerts
    rules:
      - alert: HighRateLimitHits
        expr: |
          sum(rate(business_operations_total{operation=~"rate_limit_hit"}[5m])) > 5
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High rate limit violations"
          description: "Rate limit violations at {{ $value }} per second. Possible abuse or misconfiguration."

      - alert: UnusualRequestVolume
        expr: |
          sum(rate(http_requests_total[5m])) by (service) > 
          (avg_over_time(sum(rate(http_requests_total[5m])) by (service)[1h:5m]) * 3)
        for: 3m
        labels:
          severity: warning
        annotations:
          summary: "Unusual request volume for {{ $labels.service }}"
          description: "{{ $labels.service }} is receiving {{ $value }} requests/sec, which is 3x the hourly average."

      - alert: ErrorSpike
        expr: |
          (
            sum(rate(http_requests_total{status=~"4..|5.."}[2m])) by (service) >
            (avg_over_time(sum(rate(http_requests_total{status=~"4..|5.."}[2m])) by (service)[30m:2m]) * 5)
          ) and (
            sum(rate(http_requests_total{status=~"4..|5.."}[2m])) by (service) > 1
          )
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Error spike detected for {{ $labels.service }}"
          description: "{{ $labels.service }} error rate is {{ $value }}/sec, which is 5x the 30-minute average."

  - name: kafka_alerts
    rules:
      - alert: HighKafkaConsumerLag
        expr: |
          kafka_consumer_lag > 1000
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High Kafka consumer lag for {{ $labels.service }}"
          description: "{{ $labels.service }} has consumer lag of {{ $value }} messages on topic {{ $labels.topic }}."

      - alert: CriticalKafkaConsumerLag
        expr: |
          kafka_consumer_lag > 10000
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Critical Kafka consumer lag for {{ $labels.service }}"
          description: "{{ $labels.service }} has consumer lag of {{ $value }} messages on topic {{ $labels.topic }}."

      - alert: KafkaMessageProcessingErrors
        expr: |
          sum(rate(kafka_errors_total[5m])) by (service, topic) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Kafka message processing errors in {{ $labels.service }}"
          description: "{{ $labels.service }} is experiencing {{ $value }} Kafka errors/sec on topic {{ $labels.topic }}."

  - name: benchmark_alerts
    rules:
      - alert: BenchmarkPerformanceDegradation
        expr: |
          benchmark_duration_seconds > 
          (avg_over_time(benchmark_duration_seconds[1h]) * 1.5)
        for: 3m
        labels:
          severity: warning
        annotations:
          summary: "Performance degradation detected in {{ $labels.service }}"
          description: "{{ $labels.service }} {{ $labels.test }} benchmark duration is {{ $value }}s, which is 50% slower than the hourly average."

      - alert: BenchmarkErrorRateIncrease
        expr: |
          (
            sum(rate(benchmark_errors_total[5m])) by (service, test) /
            sum(rate(benchmark_operations_total[5m])) by (service, test)
          ) * 100 > 1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Benchmark error rate increase for {{ $labels.service }}"
          description: "{{ $labels.service }} {{ $labels.test }} benchmark has error rate of {{ $value }}%."

      - alert: PossibleMemoryLeak
        expr: |
          increase(benchmark_memory_usage_bytes[30m]) > (100 * 1024 * 1024)
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Possible memory leak in {{ $labels.service }}"
          description: "{{ $labels.service }} {{ $labels.test }} memory usage increased by {{ $value | humanize }}B in 30 minutes."

  - name: infrastructure_alerts
    rules:
      - alert: ContainerNearMemoryLimit
        expr: |
          (container_memory_usage_bytes / container_spec_memory_limit_bytes) * 100 > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Container {{ $labels.name }} near memory limit"
          description: "Container {{ $labels.name }} is using {{ $value }}% of its memory limit."

      - alert: ContainerNearCPULimit
        expr: |
          (rate(container_cpu_usage_seconds_total[5m]) / container_spec_cpu_quota) * 100 > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Container {{ $labels.name }} near CPU limit"
          description: "Container {{ $labels.name }} is using {{ $value }}% of its CPU limit."

      - alert: LowDiskSpace
        expr: |
          (node_filesystem_avail_bytes / node_filesystem_size_bytes) * 100 < 20
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Low disk space on {{ $labels.instance }}"
          description: "Disk space on {{ $labels.instance }} {{ $labels.mountpoint }} is {{ $value }}% full."

      - alert: CriticalDiskSpace
        expr: |
          (node_filesystem_avail_bytes / node_filesystem_size_bytes) * 100 < 10
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Critical disk space on {{ $labels.instance }}"
          description: "Disk space on {{ $labels.instance }} {{ $labels.mountpoint }} is {{ $value }}% full."

  - name: sla_alerts
    rules:
      - alert: SLAViolation_ResponseTime
        expr: |
          (
            histogram_quantile(0.95, 
              sum(rate(http_request_duration_seconds_bucket[5m])) by (service, le)
            ) > 2
          ) and (
            sum(rate(http_requests_total[5m])) by (service) > 1
          )
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "SLA violation: Response time for {{ $labels.service }}"
          description: "{{ $labels.service }} 95th percentile response time is {{ $value }}s, violating SLA of 2s."

      - alert: SLAViolation_Availability
        expr: |
          (
            sum(rate(http_requests_total{status!~"5.."}[5m])) by (service) /
            sum(rate(http_requests_total[5m])) by (service)
          ) * 100 < 99.5
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "SLA violation: Availability for {{ $labels.service }}"
          description: "{{ $labels.service }} availability is {{ $value }}%, violating SLA of 99.5%."

      - alert: SLAViolation_ErrorRate
        expr: |
          (
            sum(rate(http_requests_total{status=~"5.."}[5m])) by (service) /
            sum(rate(http_requests_total[5m])) by (service)
          ) * 100 > 0.5
        for: 3m
        labels:
          severity: critical
        annotations:
          summary: "SLA violation: Error rate for {{ $labels.service }}"
          description: "{{ $labels.service }} error rate is {{ $value }}%, violating SLA of 0.5%."
