#!/bin/bash

set -e

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

GRAFANA_URL="http://localhost:3000"
GRAFANA_USER="admin"
GRAFANA_PASSWORD="admin123"
DASHBOARD_DIR="grafana/dashboards"

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_grafana_health() {
    print_status "Checking Grafana health..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s -f "$GRAFANA_URL/api/health" > /dev/null 2>&1; then
            print_success "<PERSON><PERSON> is healthy and ready"
            return 0
        fi
        
        print_status "Waiting for <PERSON><PERSON> to be ready... (attempt $attempt/$max_attempts)"
        sleep 2
        attempt=$((attempt + 1))
    done
    
    print_error "Grafana is not responding after $max_attempts attempts"
    return 1
}

import_dashboard() {
    local dashboard_file="$1"
    local dashboard_name=$(basename "$dashboard_file" .json)
    
    print_status "Importing dashboard: $dashboard_name"
    
    local dashboard_json=$(cat "$dashboard_file")

    local folder_id=0
    if echo "$dashboard_json" | jq -r '.title' | grep -E "(Overview|Infrastructure|Business)" > /dev/null; then
        folder_id=1
    elif echo "$dashboard_json" | jq -r '.title' | grep -E "(Service.*Details|Detailed Metrics)" > /dev/null; then
        folder_id=2
    elif echo "$dashboard_json" | jq -r '.title' | grep -i "benchmark" > /dev/null; then
        folder_id=3
    fi

    local import_payload=$(echo "$dashboard_json" | jq --arg folder_id "$folder_id" '{
        dashboard: .,
        folderId: ($folder_id | tonumber),
        overwrite: true
    }')
    
    local response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -u "$GRAFANA_USER:$GRAFANA_PASSWORD" \
        -d "$import_payload" \
        "$GRAFANA_URL/api/dashboards/db")
    
    if echo "$response" | jq -e '.status == "success"' > /dev/null 2>&1; then
        print_success "Successfully imported: $dashboard_name"
    else
        local error_message=$(echo "$response" | jq -r '.message // "Unknown error"')
        print_warning "Failed to import $dashboard_name: $error_message"
    fi
}

main() {
    print_status "Starting production dashboard import..."
    
    if [ ! -d "$DASHBOARD_DIR" ]; then
        print_error "Dashboard directory not found: $DASHBOARD_DIR"
        print_error "Please run this script from the coupon-monitoring directory"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        print_error "jq is required but not installed. Please install jq first."
        exit 1
    fi
    
    if ! check_grafana_health; then
        print_error "Cannot connect to Grafana. Please ensure it's running."
        exit 1
    fi
    
    print_status "Importing production dashboards from organized folders..."

    local dashboard_count=0
    local success_count=0
    
    for folder in "$DASHBOARD_DIR"/overview "$DASHBOARD_DIR"/details "$DASHBOARD_DIR"/benchmark; do
        if [ -d "$folder" ]; then
            print_status "Processing folder: $(basename "$folder")"
            for dashboard_file in "$folder"/*.json; do
                if [ -f "$dashboard_file" ]; then
                    import_dashboard "$dashboard_file"
                    dashboard_count=$((dashboard_count + 1))
                    success_count=$((success_count + 1))
                fi
            done
        fi
    done

    print_success "Dashboard import completed!"
    print_success "Imported $success_count out of $dashboard_count dashboards"
    print_status "Access your dashboards at: $GRAFANA_URL"
    print_status "Login credentials: $GRAFANA_USER / $GRAFANA_PASSWORD"
}

main "$@"

