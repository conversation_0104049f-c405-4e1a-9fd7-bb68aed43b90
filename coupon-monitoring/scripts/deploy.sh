#!/bin/bash

set -e

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
MONITORING_DIR="$PROJECT_ROOT"

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

command_exists() {
    command -v "$1" >/dev/null 2>&1
}

create_directories() {
    print_status "Creating monitoring directories..."
    
    mkdir -p "$MONITORING_DIR/prometheus/data"
    mkdir -p "$MONITORING_DIR/grafana/data"
    mkdir -p "$MONITORING_DIR/alertmanager/data"
    
    sudo chown -R 472:472 "$MONITORING_DIR/grafana/data" 2>/dev/null || true
    
    print_success "Monitoring directories created"
}

validate_configs() {
    print_status "Validating configuration files..."
    
    if [ ! -f "$MONITORING_DIR/prometheus/prometheus.yml" ]; then
        print_error "Prometheus configuration file not found: $MONITORING_DIR/prometheus/prometheus.yml"
        exit 1
    fi
    
    if [ ! -f "$MONITORING_DIR/alertmanager/alertmanager.yml" ]; then
        print_error "AlertManager configuration file not found: $MONITORING_DIR/alertmanager/alertmanager.yml"
        exit 1
    fi
    
    if [ ! -d "$MONITORING_DIR/grafana/provisioning" ]; then
        print_error "Grafana provisioning directory not found: $MONITORING_DIR/grafana/provisioning"
        exit 1
    fi
    
    print_success "Configuration files validated"
}

start_monitoring() {
    print_status "Starting monitoring stack..."
    
    cd "$PROJECT_ROOT"
    
    docker-compose up -d
    
    print_success "Monitoring stack started"
}

wait_for_services() {
    print_status "Waiting for services to be ready..."

    print_status "Waiting for Prometheus..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if docker exec prometheus wget --no-verbose --tries=1 --spider http://localhost:9090/-/ready 2>/dev/null; then
            print_success "Prometheus is ready (internal)"
            break
        fi
        sleep 2
        timeout=$((timeout - 2))
    done

    if [ $timeout -le 0 ]; then
        print_error "Prometheus failed to start within timeout"
        exit 1
    fi
    
    print_status "Waiting for Grafana..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if curl -s http://localhost:3000/api/health >/dev/null 2>&1; then
            print_success "Grafana is ready"
            break
        fi
        sleep 2
        timeout=$((timeout - 2))
    done
    
    if [ $timeout -le 0 ]; then
        print_error "Grafana failed to start within timeout"
        exit 1
    fi
    
    print_status "Waiting for AlertManager..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if curl -s http://localhost:9093/-/ready >/dev/null 2>&1; then
            print_success "AlertManager is ready"
            break
        fi
        sleep 2
        timeout=$((timeout - 2))
    done
    
    if [ $timeout -le 0 ]; then
        print_error "AlertManager failed to start within timeout"
        exit 1
    fi
}

display_urls() {
    print_success "Monitoring stack deployed successfully!"
    echo ""
    echo "Service URLs (Production/Secure Mode):"
    echo "  Grafana:      http://localhost:3000 (admin/admin123)"
    echo "  Push Gateway: http://localhost:9091"
    echo "  AlertManager: http://localhost:9093"
    echo ""
    echo "Internal Only (use Grafana for access):"
    echo "  Prometheus:   Internal only (use Grafana)"
    echo "  Node Exporter: Internal only (metrics via Grafana)"
    echo "  cAdvisor:     Internal only (metrics via Grafana)"
    echo ""
    echo "Default Grafana credentials:"
    echo "  Username: admin"
    echo "  Password: admin123"
    echo ""
    echo "For debugging access: make monitoring-debug"
    echo "To view logs: docker-compose logs -f [service]"
    echo "To stop: docker-compose down"
}

run_health_checks() {
    print_status "Running health checks..."

    print_status "Checking Prometheus targets (internal)..."
    if docker exec prometheus wget --no-verbose --tries=1 --spider http://localhost:9090/api/v1/targets 2>/dev/null; then
        targets_response=$(docker exec prometheus wget -qO- http://localhost:9090/api/v1/targets 2>/dev/null)
        if echo "$targets_response" | grep -q '"health":"up"'; then
            print_success "Some Prometheus targets are healthy"
        else
            print_warning "No healthy Prometheus targets found. Services may not be running."
        fi
    else
        print_error "Cannot reach Prometheus for health check"
    fi

    print_status "Checking Grafana datasources..."
    if curl -s -u admin:admin123 http://localhost:3000/api/datasources | grep -q "Prometheus"; then
        print_success "Grafana Prometheus datasource configured"
    else
        print_warning "Grafana Prometheus datasource not found"
    fi

    print_status "Checking Push Gateway..."
    if curl -s http://localhost:9091/metrics >/dev/null 2>&1; then
        print_success "Push Gateway is accessible"
    else
        print_warning "Push Gateway is not accessible"
    fi
}

main() {
    echo "=================================================="
    echo "  Coupon System - Monitoring Deployment Script"
    echo "=================================================="
    echo ""
    
    create_directories
    validate_configs
    start_monitoring
    wait_for_services
    run_health_checks
    display_urls
    
    echo ""
    print_success "Deployment completed successfully!"
}

case "${1:-}" in
    "start")
        start_monitoring
        wait_for_services
        display_urls
        ;;
    "stop")
        print_status "Stopping monitoring stack..."
        cd "$PROJECT_ROOT"
        docker-compose down
        print_success "Monitoring stack stopped"
        ;;
    "restart")
        print_status "Restarting monitoring stack..."
        cd "$PROJECT_ROOT"
        docker-compose down
        docker-compose up -d
        wait_for_services
        display_urls
        ;;
    "status")
        print_status "Checking monitoring stack status..."
        cd "$PROJECT_ROOT"
        docker-compose ps
        ;;
    "health")
        run_health_checks
        ;;
     "help")
        echo "Usage: $0 [start|stop|restart|status|logs|health|help]"
        echo ""
        echo "Coupon System - Monitoring Deployment Script"
        echo "============================================="
        echo ""
        echo "This script manages the monitoring stack deployment including:"
        echo "- Prometheus (metrics collection) - Internal only"
        echo "- Grafana (visualization dashboards) - External access"
        echo "- AlertManager (alerting) - External access"
        echo "- Node Exporter (system metrics) - Internal only"
        echo "- cAdvisor (container metrics) - Internal only"
        echo "- Push Gateway (benchmark metrics) - External access"
        echo ""
        echo "Commands:"
        echo "  (no args)  Deploy complete monitoring stack (default)"
        echo "  start      Start monitoring services"
        echo "  stop       Stop monitoring services"
        echo "  restart    Restart monitoring services"
        echo "  status     Show monitoring services status"
        echo "  logs       Show monitoring services logs"
        echo "             Use: $0 logs [service-name] for specific service"
        echo "  health     Run health checks on monitoring services"
        echo "  help       Show this help message"
        echo ""
        echo "Examples:"
        echo "  $0                    # Deploy complete stack"
        echo "  $0 start              # Start services"
        echo "  $0 logs prometheus    # Show Prometheus logs"
        echo "  $0 health             # Check service health"
        echo ""
        echo "Service URLs (after deployment):"
        echo "  Grafana:      http://localhost:3000 (admin/admin123)"
        echo "  Push Gateway: http://localhost:9091"
        echo "  AlertManager: http://localhost:9093"
        echo ""
        echo "Internal Only (use Grafana for access):"
        echo "  Prometheus:   Internal only (use Grafana)"
        echo "  Node Exporter: Internal only (metrics via Grafana)"
        echo "  cAdvisor:     Internal only (metrics via Grafana)"
        echo ""
        echo "Security Note:"
        echo "  This deployment uses a secure configuration with internal-only"
        echo "  access for Prometheus, Node Exporter, and cAdvisor."
        echo "  Use 'make monitoring-debug' for temporary debugging access."
        echo ""
        echo "Prerequisites:"
        echo "  - Docker and Docker Compose installed"
        echo "  - Docker daemon running"
        ;;
    *)
        main
        ;;
esac
