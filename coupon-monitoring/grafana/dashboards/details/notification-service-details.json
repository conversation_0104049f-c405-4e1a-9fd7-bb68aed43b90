{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 1, "id": null, "links": [{"asDropdown": false, "icon": "external link", "includeVars": false, "keepTime": false, "tags": ["production"], "targetBlank": true, "title": "Back to Overview", "tooltip": "", "type": "dashboards", "url": ""}], "liveNow": false, "panels": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Key metrics for Notification Service", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Service Uptime"}, "properties": [{"id": "unit", "value": "percent"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 95}, {"color": "green", "value": 99}]}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Error Rate"}, "properties": [{"id": "unit", "value": "percent"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "red", "value": 5}]}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Request Rate"}, "properties": [{"id": "unit", "value": "reqps"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Avg Response Time"}, "properties": [{"id": "unit", "value": "ms"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 100}, {"color": "red", "value": 500}]}}]}]}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}, "id": 1, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.5.1", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "up{job=\"notification-service\"} * 100", "interval": "", "legendFormat": "Service Uptime", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(grpc_server_handled_total{service=\"notification-service\"}[5m]))", "interval": "", "legendFormat": "Request Rate", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(grpc_server_handled_total{service=\"notification-service\", grpc_code!=\"OK\"}[5m])) / sum(rate(grpc_server_handled_total{service=\"notification-service\"}[5m])) * 100", "interval": "", "legendFormat": "Error Rate", "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "histogram_quantile(0.50, sum(rate(grpc_server_handling_seconds_bucket{service=\"notification-service\"}[5m])) by (le)) * 1000", "interval": "", "legendFormat": "Avg Response Time", "refId": "D"}], "title": "Notification Service - Key Metrics", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Notification operations by type and status", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "Notifications/sec", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 2, "options": {"legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum by(operation, status) (rate(business_operations_total{service=\"notification-service\", operation=~\"notification_.*\"}[5m]))", "interval": "", "legendFormat": "{{operation}} - {{status}}", "refId": "A"}], "title": "Notification Operations by Type", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Kafka message processing performance", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "Messages/sec", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "id": 3, "options": {"legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum by(topic, status) (rate(kafka_messages_total{service=\"notification-service\"}[5m]))", "interval": "", "legendFormat": "{{topic}} - {{status}}", "refId": "A"}], "title": "Kafka Message Processing", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Notification delivery success rate by type", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "Success Rate %", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 95}, {"color": "green", "value": 99}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "id": 4, "options": {"legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum by(notification_type) (rate(business_operations_total{service=\"notification-service\", operation=~\"notification_.*\", status=\"success\"}[5m])) / sum by(notification_type) (rate(business_operations_total{service=\"notification-service\", operation=~\"notification_.*\"}[5m])) * 100", "interval": "", "legendFormat": "{{notification_type}}", "refId": "A"}], "title": "Notification Success Rate by Type", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Database query performance for notification service", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "Duration", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 0.1}, {"color": "red", "value": 0.5}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "id": 5, "options": {"legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "histogram_quantile(0.95, sum by(operation, le) (rate(database_query_duration_seconds_bucket{service=\"notification-service\"}[5m])))", "interval": "", "legendFormat": "P95 - {{operation}}", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "histogram_quantile(0.50, sum by(operation, le) (rate(database_query_duration_seconds_bucket{service=\"notification-service\"}[5m])))", "interval": "", "legendFormat": "P50 - {{operation}}", "refId": "B"}], "title": "Database Query Latency", "type": "timeseries"}], "refresh": "10s", "schemaVersion": 38, "style": "dark", "tags": ["production", "notification-service", "microservice"], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Notification Service - Detailed Metrics", "uid": "notification-service-details", "version": 1, "weekStart": ""}