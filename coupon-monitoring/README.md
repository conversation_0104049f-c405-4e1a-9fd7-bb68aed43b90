# Coupon Microservice Monitoring Stack

A comprehensive monitoring solution for the coupon microservice system using Prometheus, Grafana, and Alertmanager.

## 🏗️ Architecture Overview

The monitoring stack consists of the following components:

- **Prometheus** (v2.45.0) - Metrics collection and storage
- **Grafana** (v10.0.0) - Visualization dashboards and analytics
- **Alertmanager** (v0.25.0) - Alert routing and notification management
- **Node Exporter** (v1.6.0) - System and hardware metrics
- **cAdvisor** (v0.47.0) - Container resource usage and performance metrics

## 📋 Prerequisites

Before deploying the monitoring stack, ensure you have:

- **Docker** (v20.10+) and **Docker Compose** (v2.0+) installed
- **Docker daemon** running
- **Sufficient system resources**:
  - RAM: 4GB minimum (8GB recommended)
  - Disk: 10GB free space for metrics storage
  - CPU: 2 cores minimum
- **Network ports available**:
  - 3000 (Grafana)
  - 9090 (Prometheus)
  - 9093 (Alertmanager)
  - 9100 (Node Exporter)
  - 8080 (cAdvisor)

## 🚀 Quick Start

### Deploy Complete Stack

```bash
# Deploy the entire monitoring stack
make deploy

# Or use the deployment script directly
./scripts/deploy.sh
```

### Individual Operations

```bash
# Start services
make monitoring-start

# Stop services
make monitoring-stop

# Restart services
make monitoring-restart

# Check status
make monitoring-status

# View logs
make monitoring-logs SERVICE=prometheus

# Run health checks
make monitoring-health
```

## 🔧 Configuration

### Prometheus Configuration

- **Config file**: `prometheus/prometheus.yml`
- **Scrape interval**: 15 seconds
- **Retention**: 30 days
- **Monitored services**:
  - API Gateway (port 2112)
  - Auth Service (port 2112)
  - User Service (port 2112)
  - Voucher Service (port 2112)
  - Product Service (port 2112)
  - Order Service (port 2112)
  - Notification Service (port 2112)

### Grafana Configuration

- **Default credentials**: admin/admin123
- **Data source**: Prometheus (auto-configured)
- **Dashboards**: Located in `grafana/dashboards/`
- **Provisioning**: Automatic via `grafana/provisioning/`

### Alertmanager Configuration

- **Config file**: `alertmanager/alertmanager.yml`
- **Email notifications**: Configured for ops-team and dev-team
- **Webhook support**: Available for external integrations
- **Alert routing**: Based on severity levels (critical, warning)

## 📊 Service URLs

After deployment, access the services at:

| Service       | URL                   | Credentials    |
| ------------- | --------------------- | -------------- |
| Grafana       | http://localhost:3000 | admin/admin123 |
| Prometheus    | http://localhost:9090 | -              |
| Alertmanager  | http://localhost:9093 | -              |
| Node Exporter | http://localhost:9100 | -              |
| cAdvisor      | http://localhost:8080 | -              |

## 🔍 Monitoring Features

### Metrics Collection

- **Application metrics**: Custom business metrics from microservices
- **System metrics**: CPU, memory, disk, network via Node Exporter
- **Container metrics**: Docker container resource usage via cAdvisor
- **Service health**: HTTP endpoint monitoring and availability

### Alerting Rules

- **Service Down**: Triggers when services become unavailable
- **High CPU Usage**: Alerts on sustained high CPU utilization
- **Memory Usage**: Monitors memory consumption thresholds
- **Disk Space**: Warns when disk usage exceeds limits
- **Response Time**: Alerts on slow API response times

### Dashboards

- **System Overview**: High-level system health and performance
- **Service Metrics**: Individual microservice performance
- **Infrastructure**: Hardware and container resource usage
- **Alerts**: Current alert status and history

## 🛠️ Troubleshooting

### Common Issues

#### 1. Alertmanager Configuration Errors

**Problem**: YAML unmarshal errors with "field subject/body not found"
**Solution**: The configuration has been updated for Alertmanager v0.25.0 compatibility. Use `html` and `text` fields instead of `body`.

#### 2. Grafana Permission Issues

**Problem**: Grafana container fails to start due to permission errors
**Solution**: The deployment script automatically sets ownership to UID/GID 472:472:

```bash
sudo chown -R 472:472 "$MONITORING_DIR/grafana/data"
```

#### 3. Services Not Appearing in Prometheus

**Problem**: Microservices not showing up in Prometheus targets
**Solution**:

- Ensure services are running and exposing metrics on port 2112
- Check that services are on the `coupon-network` Docker network
- Verify service names match those in `prometheus.yml`

#### 4. Network Connectivity Issues

**Problem**: Services cannot communicate with each other
**Solution**: Ensure the `coupon-network` Docker network exists:

```bash
docker network create coupon-network
```

### Debugging Commands

```bash
# Check container status
docker-compose ps

# View service logs
docker-compose logs -f [service-name]

# Test Prometheus targets
curl http://localhost:9090/api/v1/targets

# Test Grafana health
curl http://localhost:3000/api/health

# Test Alertmanager
curl http://localhost:9093/-/healthy
```

## 🔒 Security Considerations

- **Default passwords**: Change Grafana admin password in production
- **Network isolation**: Services communicate via Docker network
- **Email credentials**: Update SMTP settings in `alertmanager.yml`
- **Webhook security**: Implement authentication for webhook endpoints

## 📈 Performance Tuning

### Prometheus Optimization

- **Storage retention**: Adjust `--storage.tsdb.retention.time` for your needs
- **Scrape intervals**: Balance between data granularity and resource usage
- **Memory usage**: Monitor Prometheus memory consumption

### Grafana Optimization

- **Dashboard queries**: Optimize PromQL queries for better performance
- **Refresh intervals**: Set appropriate dashboard refresh rates
- **Plugin management**: Only install necessary plugins

## 🔄 Backup and Recovery

### Backup Data

```bash
# Backup all monitoring data
make monitoring-backup
```

### Restore Data

```bash
# Restore from specific backup
make monitoring-restore BACKUP_DATE=20231201_120000
```

## 🧪 Testing and Benchmarking

The monitoring stack includes comprehensive benchmarking tools:

```bash
# Run all service benchmarks
make benchmark-all

# Run specific service benchmarks
make benchmark-user
make benchmark-voucher
make benchmark-product

# Generate benchmark reports
make benchmark-report
```

## 🤝 Integration with Microservices

### Metrics Endpoint Requirements

Each microservice should expose metrics at:

- **Path**: `/metrics`
- **Port**: `2112`
- **Format**: Prometheus format

### Custom Metrics

Services can expose custom business metrics:

```go
// Example Go metrics
var (
    requestsTotal = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "http_requests_total",
            Help: "Total number of HTTP requests",
        },
        []string{"method", "endpoint", "status"},
    )
)
```

## 📚 Additional Resources

- [Prometheus Documentation](https://prometheus.io/docs/)
- [Grafana Documentation](https://grafana.com/docs/)
- [Alertmanager Documentation](https://prometheus.io/docs/alerting/latest/alertmanager/)
- [Docker Compose Reference](https://docs.docker.com/compose/)

## 🆘 Support

For issues and questions:

1. Check the troubleshooting section above
2. Review service logs using `make monitoring-logs`
3. Run health checks with `make monitoring-health`
4. Consult the official documentation for each component
