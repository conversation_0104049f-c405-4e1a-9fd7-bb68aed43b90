package handler

import (
	"context"
	"math"
	"time"

	"gitlab.zalopay.vn/phunn4/coupon-product-service/internal/model"
	"gitlab.zalopay.vn/phunn4/coupon-product-service/internal/service"
	proto_common_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/common/v1"
	proto_product_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/product/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/errors"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type ProductServer struct {
	proto_product_v1.UnimplementedProductServiceServer
	svc service.ProductService
}

func NewProductServer(svc service.ProductService) *ProductServer {
	return &ProductServer{svc: svc}
}

func (s *ProductServer) GetProduct(ctx context.Context, req *proto_product_v1.GetProductRequest) (*proto_product_v1.GetProductResponse, error) {
	product, err := s.svc.GetProductByID(ctx, req.ProductId)
	if err != nil {
		return &proto_product_v1.GetProductResponse{
			Error: convertErrorToServiceError(err),
		}, nil
	}

	return &proto_product_v1.GetProductResponse{
		Product: convertModelProductToProto(product),
	}, nil
}

func (s *ProductServer) UpdateProduct(ctx context.Context, req *proto_product_v1.UpdateProductRequest) (*proto_product_v1.UpdateProductResponse, error) {
	updateReq := &model.UpdateProductRequest{
		Name:          req.Name,
		Description:   req.Description,
		Price:         req.Price,
		CategoryID:    req.CategoryId,
		ImageURL:      req.ImageUrl,
		StockQuantity: req.StockQuantity,
		Status:        convertProtoStatusToModel(req.Status),
		Brand:         req.Brand,
		SKU:           req.Sku,
	}

	product, err := s.svc.UpdateProduct(ctx, req.ProductId, updateReq)
	if err != nil {
		return &proto_product_v1.UpdateProductResponse{
			Error: convertErrorToServiceError(err),
		}, nil
	}

	return &proto_product_v1.UpdateProductResponse{
		Product: convertModelProductToProto(product),
	}, nil
}

func (s *ProductServer) ListProducts(ctx context.Context, req *proto_product_v1.ListProductsRequest) (*proto_product_v1.ListProductsResponse, error) {
	listReq := &model.ListProductsRequest{
		Page:       int(req.Pagination.Page),
		Limit:      int(req.Pagination.PageSize),
		Search:     req.Search,
		CategoryID: &req.CategoryId,
		Status:     convertProtoStatusToModel(req.Status),
		SortBy:     req.SortBy,
		SortOrder:  req.SortOrder,
	}

	products, total, err := s.svc.ListProducts(ctx, listReq)
	if err != nil {
		return &proto_product_v1.ListProductsResponse{
			Error: convertErrorToServiceError(err),
		}, nil
	}

	protoProducts := make([]*proto_product_v1.Product, len(products))
	for i, product := range products {
		protoProducts[i] = convertModelProductToProto(product)
	}

	totalPages := int32(math.Ceil(float64(total) / float64(listReq.Limit)))

	return &proto_product_v1.ListProductsResponse{
		Products: protoProducts,
		Pagination: &proto_common_v1.PaginationResponse{
			CurrentPage: int32(listReq.Page),
			PageSize:    int32(listReq.Limit),
			TotalItems:  total,
			TotalPages:  totalPages,
		},
	}, nil
}

func (s *ProductServer) ListCategories(ctx context.Context, req *proto_product_v1.ListCategoriesRequest) (*proto_product_v1.ListCategoriesResponse, error) {
	categories, err := s.svc.ListCategories(ctx)
	if err != nil {
		return &proto_product_v1.ListCategoriesResponse{
			Error: convertErrorToServiceError(err),
		}, nil
	}

	protoCategories := make([]*proto_product_v1.Category, len(categories))
	for i, category := range categories {
		protoCategories[i] = convertModelCategoryToProto(category)
	}

	return &proto_product_v1.ListCategoriesResponse{
		Categories: protoCategories,
	}, nil
}

func (s *ProductServer) HealthCheck(ctx context.Context, req *proto_common_v1.HealthCheckRequest) (*proto_common_v1.HealthCheckResponse, error) {
	return &proto_common_v1.HealthCheckResponse{
		Status: proto_common_v1.HealthCheckResponse_SERVING_STATUS_SERVING,
	}, nil
}

func convertErrorToServiceError(err error) *proto_common_v1.ServiceError {
	if appErr, ok := err.(*errors.AppError); ok {
		return &proto_common_v1.ServiceError{
			Code:        convertHTTPStatusToStatusCode(appErr.Code),
			Message:     appErr.Message,
			Timestamp:   timestamppb.New(time.Now()),
			ServiceName: "product-service",
		}
	}

	return &proto_common_v1.ServiceError{
		Code:        proto_common_v1.StatusCode_STATUS_CODE_INTERNAL,
		Message:     err.Error(),
		Timestamp:   timestamppb.New(time.Now()),
		ServiceName: "product-service",
	}
}

func convertHTTPStatusToStatusCode(httpStatus int) proto_common_v1.StatusCode {
	switch httpStatus {
	case 400:
		return proto_common_v1.StatusCode_STATUS_CODE_INVALID_ARGUMENT
	case 404:
		return proto_common_v1.StatusCode_STATUS_CODE_NOT_FOUND
	case 401:
		return proto_common_v1.StatusCode_STATUS_CODE_UNAUTHENTICATED
	case 403:
		return proto_common_v1.StatusCode_STATUS_CODE_PERMISSION_DENIED
	case 409:
		return proto_common_v1.StatusCode_STATUS_CODE_ALREADY_EXISTS
	case 408:
		return proto_common_v1.StatusCode_STATUS_CODE_DEADLINE_EXCEEDED
	case 503:
		return proto_common_v1.StatusCode_STATUS_CODE_UNAVAILABLE
	default:
		return proto_common_v1.StatusCode_STATUS_CODE_INTERNAL
	}
}

func convertProtoStatusToModel(status proto_product_v1.ProductStatus) model.ProductStatus {
	switch status {
	case proto_product_v1.ProductStatus_PRODUCT_STATUS_ACTIVE:
		return model.ProductStatusActive
	case proto_product_v1.ProductStatus_PRODUCT_STATUS_INACTIVE:
		return model.ProductStatusInactive
	default:
		return model.ProductStatusActive
	}
}

func convertModelStatusToProto(status model.ProductStatus) proto_product_v1.ProductStatus {
	switch status {
	case model.ProductStatusActive:
		return proto_product_v1.ProductStatus_PRODUCT_STATUS_ACTIVE
	case model.ProductStatusInactive:
		return proto_product_v1.ProductStatus_PRODUCT_STATUS_INACTIVE
	default:
		return proto_product_v1.ProductStatus_PRODUCT_STATUS_ACTIVE
	}
}

func convertModelProductToProto(product *model.Product) *proto_product_v1.Product {
	protoProduct := &proto_product_v1.Product{
		Id:            product.ID,
		Name:          product.Name,
		Description:   product.Description,
		Price:         product.Price,
		CategoryId:    product.CategoryID,
		ImageUrl:      product.ImageURL,
		StockQuantity: product.StockQuantity,
		Status:        convertModelStatusToProto(product.Status),
		Brand:         product.Brand,
		Sku:           product.SKU,
		CreatedAt:     timestamppb.New(product.CreatedAt),
		UpdatedAt:     timestamppb.New(product.UpdatedAt),
	}

	if product.Category != nil {
		protoProduct.Category = convertModelCategoryToProto(product.Category)
	}

	return protoProduct
}

func convertModelCategoryToProto(category *model.Category) *proto_product_v1.Category {
	return &proto_product_v1.Category{
		Id:          category.ID,
		Name:        category.Name,
		Description: category.Description,
		CreatedAt:   timestamppb.New(category.CreatedAt),
	}
}
