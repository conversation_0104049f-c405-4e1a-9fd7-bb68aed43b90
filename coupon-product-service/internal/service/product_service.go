package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	"gitlab.zalopay.vn/phunn4/coupon-product-service/internal/model"
	"gitlab.zalopay.vn/phunn4/coupon-product-service/internal/repository"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/errors"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
	"gorm.io/gorm"
)

type ProductService interface {
	GetProductByID(ctx context.Context, id uint64) (*model.Product, error)
	UpdateProduct(ctx context.Context, id uint64, req *model.UpdateProductRequest) (*model.Product, error)
	ListProducts(ctx context.Context, req *model.ListProductsRequest) ([]*model.Product, int64, error)

	ListCategories(ctx context.Context) ([]*model.Category, error)
}

type productService struct {
	repo            repository.ProductRepository
	logger          *logging.Logger
	businessMetrics *metrics.BusinessMetrics
}

func NewProductService(repo repository.ProductRepository, logger *logging.Logger, businessMetrics *metrics.BusinessMetrics) ProductService {
	return &productService{
		repo:            repo,
		logger:          logger,
		businessMetrics: businessMetrics,
	}
}

func (s *productService) GetProductByID(ctx context.Context, id uint64) (*model.Product, error) {
	start := time.Now()
	if id == 0 {
		s.businessMetrics.RecordProductView("error", time.Since(start))
		return nil, errors.NewValidationError("product ID is required", nil)
	}

	product, err := s.repo.GetProductByID(ctx, id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			s.businessMetrics.RecordProductView("error", time.Since(start))
			return nil, errors.NewNotFoundError("product not found")
		}
		s.businessMetrics.RecordProductView("error", time.Since(start))
		return nil, fmt.Errorf("failed to get product: %w", err)
	}

	s.businessMetrics.RecordProductView("success", time.Since(start))
	return product, nil
}

func (s *productService) ListProducts(ctx context.Context, req *model.ListProductsRequest) ([]*model.Product, int64, error) {
	start := time.Now()
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Limit <= 0 {
		req.Limit = 10
	}
	if req.Limit > 100 {
		req.Limit = 100
	}

	products, total, err := s.repo.ListProducts(ctx, req)
	if err != nil {
		s.logger.WithContext(ctx).Errorf("Failed to list products: %v", err)
		s.businessMetrics.RecordProductSearch("error", time.Since(start))
		return nil, 0, fmt.Errorf("failed to list products: %w", err)
	}

	s.businessMetrics.RecordProductSearch("success", time.Since(start))
	return products, total, nil
}

func (s *productService) ListCategories(ctx context.Context) ([]*model.Category, error) {
	start := time.Now()
	categories, err := s.repo.ListCategories(ctx)
	if err != nil {
		s.logger.WithContext(ctx).Errorf("Failed to list categories: %v", err)
		s.businessMetrics.RecordCategoryList("error", time.Since(start))
		return nil, fmt.Errorf("failed to list categories: %w", err)
	}

	s.businessMetrics.RecordCategoryList("success", time.Since(start))
	return categories, nil
}

func (s *productService) UpdateProduct(ctx context.Context, id uint64, req *model.UpdateProductRequest) (*model.Product, error) {
	start := time.Now()
	if id == 0 {
		s.businessMetrics.RecordProductView("error", time.Since(start))
		return nil, errors.NewValidationError("product ID is required", nil)
	}

	if err := s.validateUpdateProductRequest(req); err != nil {
		s.businessMetrics.RecordProductView("error", time.Since(start))
		return nil, err
	}

	product, err := s.repo.GetProductByID(ctx, id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			s.businessMetrics.RecordProductView("error", time.Since(start))
			return nil, errors.NewNotFoundError("product not found")
		}
		s.businessMetrics.RecordProductView("error", time.Since(start))
		return nil, fmt.Errorf("failed to get product: %w", err)
	}

	product.Name = req.Name
	product.Description = req.Description
	product.Price = req.Price
	product.CategoryID = req.CategoryID
	product.ImageURL = req.ImageURL
	product.StockQuantity = req.StockQuantity
	product.Status = req.Status
	product.Brand = req.Brand
	product.SKU = req.SKU

	if err := s.repo.UpdateProduct(ctx, product); err != nil {
		s.logger.WithContext(ctx).Errorf("Failed to update product: %v", err)
		s.businessMetrics.RecordProductView("error", time.Since(start))
		return nil, fmt.Errorf("failed to update product: %w", err)
	}

	s.logger.WithContext(ctx).Infof("Product updated successfully: %d", product.ID)
	s.businessMetrics.RecordProductView("success", time.Since(start))
	return product, nil
}

func (s *productService) validateUpdateProductRequest(req *model.UpdateProductRequest) error {
	if strings.TrimSpace(req.Name) == "" {
		return errors.NewValidationError("product name is required", map[string]any{
			"field": "name",
		})
	}

	if req.Price <= 0 {
		return errors.NewValidationError("product price must be greater than 0", map[string]any{
			"field": "price",
			"value": req.Price,
		})
	}

	if req.StockQuantity < 0 {
		return errors.NewValidationError("stock quantity cannot be negative", map[string]any{
			"field": "stock_quantity",
			"value": req.StockQuantity,
		})
	}

	if req.Status != model.ProductStatusActive && req.Status != model.ProductStatusInactive {
		return errors.NewValidationError("invalid product status", map[string]any{
			"field": "status",
			"value": req.Status,
		})
	}

	return nil
}
