package middleware

import (
	"context"
	"path"
	"slices"
	"time"

	"github.com/labstack/echo/v4"
	"gitlab.zalopay.vn/phunn4/coupon-product-service/internal/clients"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
)

var publicMethods = map[string]bool{
	"HealthCheck": true,
}

var methodAllowList = map[string][]string{
	"api-gateway":     {"GetProduct", "UpdateProduct", "ListProducts", "ListCategories"},
	"voucher-service": {"GetProduct", "ListProducts", "ListCategories"},
	"order-service":   {"GetProduct", "ListProducts", "ListCategories"},
}

func CreateServiceAuthFunc(authClient *clients.AuthClient) func(context.Context) (context.Context, error) {
	return func(ctx context.Context) (context.Context, error) {
		ts := grpc.ServerTransportStreamFromContext(ctx)
		method := ""
		if ts != nil {
			method = path.Base(ts.Method())
		}

		if publicMethods[method] {
			return ctx, nil
		}

		md, ok := metadata.FromIncomingContext(ctx)
		if !ok {
			return nil, status.Error(codes.Unauthenticated, "missing metadata")
		}

		clientIDs := md.Get("client-id")
		clientKeys := md.Get("client-key")
		if len(clientIDs) > 0 && len(clientKeys) > 0 {
			serviceName, err := authClient.ValidateServiceCredentials(ctx, clientIDs[0], clientKeys[0])
			if err != nil {
				return nil, status.Error(codes.Unauthenticated, "invalid service credentials")
			}
			allowed := methodAllowList[serviceName]
			if !slices.Contains(allowed, method) {
				return nil, status.Errorf(codes.PermissionDenied, "method %s not allowed for service %s", method, serviceName)
			}
			return ctx, nil
		}
		return nil, status.Error(codes.Unauthenticated, "authentication required - use service credentials for gRPC calls")
	}
}

// HTTPMetricsConfig holds configuration for HTTP metrics middleware
type HTTPMetricsConfig struct {
	Metrics     *metrics.Metrics
	ServiceName string
}

// HTTPMetrics returns a middleware that records HTTP request metrics
func HTTPMetrics(config *HTTPMetricsConfig) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			start := time.Now()

			// Call the next handler
			err := next(c)

			// Record metrics
			duration := time.Since(start)
			status := c.Response().Status
			method := c.Request().Method
			endpoint := c.Path()

			// If endpoint is empty, use the request URI
			if endpoint == "" {
				endpoint = c.Request().URL.Path
			}

			config.Metrics.RecordHTTPRequest(config.ServiceName, method, endpoint, status, duration)

			// Record request and response sizes
			requestSize := float64(c.Request().ContentLength)
			if requestSize > 0 {
				config.Metrics.RecordHTTPRequestSize(config.ServiceName, method, endpoint, requestSize)
			}

			responseSize := float64(c.Response().Size)
			if responseSize > 0 {
				config.Metrics.RecordHTTPResponseSize(config.ServiceName, method, endpoint, status, responseSize)
			}

			return err
		}
	}
}
