syntax = "proto3";

package analytics.v1;

import "common/v1/common.proto";
import "common/v1/error.proto";
import "google/protobuf/timestamp.proto";

option go_package = "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/analytics/v1";

service AnalyticsService {
  // Internal analytics APIs used for service-to-service communication
  rpc TrackEvent(TrackEventRequest) returns (TrackEventResponse);
  rpc GetUserActivity(GetUserActivityRequest) returns (GetUserActivityResponse);

  // Health check
  rpc HealthCheck(common.v1.HealthCheckRequest) returns (common.v1.HealthCheckResponse);
}

message Event {
  string id = 1;
  string user_id = 2;
  string event_type = 3;
  google.protobuf.Timestamp timestamp = 4;
  map<string, string> properties = 5;
}

message TrackEventRequest {
  common.v1.RequestMetadata metadata = 1;
  string user_id = 2;
  string event_type = 3;
  map<string, string> properties = 4;
  google.protobuf.Timestamp event_timestamp = 5;
}

message TrackEventResponse {
  common.v1.ResponseMetadata metadata = 1;
  Event event = 2;
  common.v1.ServiceError error = 3;
}

message GetUserActivityRequest {
  common.v1.RequestMetadata metadata = 1;
  string user_id = 2;
  google.protobuf.Timestamp start_time = 3;
  google.protobuf.Timestamp end_time = 4;
}

message GetUserActivityResponse {
  common.v1.ResponseMetadata metadata = 1;
  repeated Event events = 2;
  common.v1.ServiceError error = 3;
}
