# buf.gen.yaml defines the code generation configuration.
version: v1
plugins:
  # This plugin generates Go code for Protobuf messages.
  - plugin: buf.build/protocolbuffers/go
    out: gen/go
    opt:
      - paths=source_relative

  # This plugin generates Go code for gRPC service clients and servers.
  - plugin: buf.build/grpc/go
    out: gen/go
    opt:
      - paths=source_relative
      - require_unimplemented_servers=false # Set to true for stricter server implementations
