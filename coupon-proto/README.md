# Protobuf API Definitions

This repository is the single source of truth for all API contracts (gRPC services and Kafka event schemas) for the Coupon Management Platform. It is managed using [Buf](https://buf.build/), a modern toolchain for Protocol Buffers.

## Structure

- **/auth, /user, etc.**: Each top-level directory represents a Bounded Context or domain. Inside, `v1` contains the Protobuf definitions for that service's API.
- **/common**: Contains shared Protobuf messages used across multiple services, such as pagination, error details, and common event metadata.
- **/gen**: Contains the auto-generated Go code produced by `buf`. This code should **not** be edited manually. It is imported by the microservice repositories.

## Toolchain

- **`buf`**: Used for linting, breaking change detection, and code generation.
- **`protoc` plugins**: `protoc-gen-go` and `protoc-gen-go-grpc` are used by `buf` to generate Go code.

## Usage

### 1. Installation

To work with this repository locally, you need to install `buf` and the necessary protoc plugins.

```bash
# Run the installation script
chmod +x scripts/install-deps.sh
make install-deps
```

This script will install `buf` and the required Go plugins into your `GOPATH/bin`.

### 2. Generating Code

After making changes to any `.proto` files, you must regenerate the Go stubs.

```bash
# Run the generation script
make generate
```

This command executes `buf generate`, which reads the configuration from `buf.gen.yaml` and outputs the generated Go code into the `gen/` directory.

### 3. Linting and Breaking Change Detection

`buf` provides powerful commands to ensure API quality and consistency.

```bash
# Lint all .proto files
make lint

# Check for breaking changes against the main branch
make breaking
```
