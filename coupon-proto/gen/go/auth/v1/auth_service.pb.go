// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: auth/v1/auth_service.proto

package v1

import (
	v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/common/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RegisterServiceRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Metadata       *v1.RequestMetadata    `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	ServiceName    string                 `protobuf:"bytes,2,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	ServiceVersion string                 `protobuf:"bytes,3,opt,name=service_version,json=serviceVersion,proto3" json:"service_version,omitempty"`
	Description    string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *RegisterServiceRequest) Reset() {
	*x = RegisterServiceRequest{}
	mi := &file_auth_v1_auth_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterServiceRequest) ProtoMessage() {}

func (x *RegisterServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_auth_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterServiceRequest.ProtoReflect.Descriptor instead.
func (*RegisterServiceRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_auth_service_proto_rawDescGZIP(), []int{0}
}

func (x *RegisterServiceRequest) GetMetadata() *v1.RequestMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *RegisterServiceRequest) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *RegisterServiceRequest) GetServiceVersion() string {
	if x != nil {
		return x.ServiceVersion
	}
	return ""
}

func (x *RegisterServiceRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type RegisterServiceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.ResponseMetadata   `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	ServiceId     uint64                 `protobuf:"varint,2,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	ClientId      string                 `protobuf:"bytes,3,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	ClientKey     string                 `protobuf:"bytes,4,opt,name=client_key,json=clientKey,proto3" json:"client_key,omitempty"`
	Error         *v1.ServiceError       `protobuf:"bytes,5,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegisterServiceResponse) Reset() {
	*x = RegisterServiceResponse{}
	mi := &file_auth_v1_auth_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterServiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterServiceResponse) ProtoMessage() {}

func (x *RegisterServiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_auth_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterServiceResponse.ProtoReflect.Descriptor instead.
func (*RegisterServiceResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_auth_service_proto_rawDescGZIP(), []int{1}
}

func (x *RegisterServiceResponse) GetMetadata() *v1.ResponseMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *RegisterServiceResponse) GetServiceId() uint64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *RegisterServiceResponse) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *RegisterServiceResponse) GetClientKey() string {
	if x != nil {
		return x.ClientKey
	}
	return ""
}

func (x *RegisterServiceResponse) GetError() *v1.ServiceError {
	if x != nil {
		return x.Error
	}
	return nil
}

type ValidateServiceCredentialsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.RequestMetadata    `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	ClientId      string                 `protobuf:"bytes,2,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	ClientKey     string                 `protobuf:"bytes,3,opt,name=client_key,json=clientKey,proto3" json:"client_key,omitempty"`
	ServiceName   string                 `protobuf:"bytes,4,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValidateServiceCredentialsRequest) Reset() {
	*x = ValidateServiceCredentialsRequest{}
	mi := &file_auth_v1_auth_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidateServiceCredentialsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateServiceCredentialsRequest) ProtoMessage() {}

func (x *ValidateServiceCredentialsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_auth_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateServiceCredentialsRequest.ProtoReflect.Descriptor instead.
func (*ValidateServiceCredentialsRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_auth_service_proto_rawDescGZIP(), []int{2}
}

func (x *ValidateServiceCredentialsRequest) GetMetadata() *v1.RequestMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *ValidateServiceCredentialsRequest) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *ValidateServiceCredentialsRequest) GetClientKey() string {
	if x != nil {
		return x.ClientKey
	}
	return ""
}

func (x *ValidateServiceCredentialsRequest) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

type ValidateServiceCredentialsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.ResponseMetadata   `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Valid         bool                   `protobuf:"varint,2,opt,name=valid,proto3" json:"valid,omitempty"`
	ServiceId     uint64                 `protobuf:"varint,3,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	ServiceName   string                 `protobuf:"bytes,4,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	Error         *v1.ServiceError       `protobuf:"bytes,5,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValidateServiceCredentialsResponse) Reset() {
	*x = ValidateServiceCredentialsResponse{}
	mi := &file_auth_v1_auth_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidateServiceCredentialsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateServiceCredentialsResponse) ProtoMessage() {}

func (x *ValidateServiceCredentialsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_auth_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateServiceCredentialsResponse.ProtoReflect.Descriptor instead.
func (*ValidateServiceCredentialsResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_auth_service_proto_rawDescGZIP(), []int{3}
}

func (x *ValidateServiceCredentialsResponse) GetMetadata() *v1.ResponseMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *ValidateServiceCredentialsResponse) GetValid() bool {
	if x != nil {
		return x.Valid
	}
	return false
}

func (x *ValidateServiceCredentialsResponse) GetServiceId() uint64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *ValidateServiceCredentialsResponse) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *ValidateServiceCredentialsResponse) GetError() *v1.ServiceError {
	if x != nil {
		return x.Error
	}
	return nil
}

var File_auth_v1_auth_service_proto protoreflect.FileDescriptor

const file_auth_v1_auth_service_proto_rawDesc = "" +
	"\n" +
	"\x1aauth/v1/auth_service.proto\x12\aauth.v1\x1a\x16common/v1/common.proto\x1a\x15common/v1/error.proto\"\xbe\x01\n" +
	"\x16RegisterServiceRequest\x126\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1a.common.v1.RequestMetadataR\bmetadata\x12!\n" +
	"\fservice_name\x18\x02 \x01(\tR\vserviceName\x12'\n" +
	"\x0fservice_version\x18\x03 \x01(\tR\x0eserviceVersion\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\"\xdc\x01\n" +
	"\x17RegisterServiceResponse\x127\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1b.common.v1.ResponseMetadataR\bmetadata\x12\x1d\n" +
	"\n" +
	"service_id\x18\x02 \x01(\x04R\tserviceId\x12\x1b\n" +
	"\tclient_id\x18\x03 \x01(\tR\bclientId\x12\x1d\n" +
	"\n" +
	"client_key\x18\x04 \x01(\tR\tclientKey\x12-\n" +
	"\x05error\x18\x05 \x01(\v2\x17.common.v1.ServiceErrorR\x05error\"\xba\x01\n" +
	"!ValidateServiceCredentialsRequest\x126\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1a.common.v1.RequestMetadataR\bmetadata\x12\x1b\n" +
	"\tclient_id\x18\x02 \x01(\tR\bclientId\x12\x1d\n" +
	"\n" +
	"client_key\x18\x03 \x01(\tR\tclientKey\x12!\n" +
	"\fservice_name\x18\x04 \x01(\tR\vserviceName\"\xe4\x01\n" +
	"\"ValidateServiceCredentialsResponse\x127\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1b.common.v1.ResponseMetadataR\bmetadata\x12\x14\n" +
	"\x05valid\x18\x02 \x01(\bR\x05valid\x12\x1d\n" +
	"\n" +
	"service_id\x18\x03 \x01(\x04R\tserviceId\x12!\n" +
	"\fservice_name\x18\x04 \x01(\tR\vserviceName\x12-\n" +
	"\x05error\x18\x05 \x01(\v2\x17.common.v1.ServiceErrorR\x05error2\xa8\x02\n" +
	"\vAuthService\x12T\n" +
	"\x0fRegisterService\x12\x1f.auth.v1.RegisterServiceRequest\x1a .auth.v1.RegisterServiceResponse\x12u\n" +
	"\x1aValidateServiceCredentials\x12*.auth.v1.ValidateServiceCredentialsRequest\x1a+.auth.v1.ValidateServiceCredentialsResponse\x12L\n" +
	"\vHealthCheck\x12\x1d.common.v1.HealthCheckRequest\x1a\x1e.common.v1.HealthCheckResponseB6Z4gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/auth/v1b\x06proto3"

var (
	file_auth_v1_auth_service_proto_rawDescOnce sync.Once
	file_auth_v1_auth_service_proto_rawDescData []byte
)

func file_auth_v1_auth_service_proto_rawDescGZIP() []byte {
	file_auth_v1_auth_service_proto_rawDescOnce.Do(func() {
		file_auth_v1_auth_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_auth_v1_auth_service_proto_rawDesc), len(file_auth_v1_auth_service_proto_rawDesc)))
	})
	return file_auth_v1_auth_service_proto_rawDescData
}

var file_auth_v1_auth_service_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_auth_v1_auth_service_proto_goTypes = []any{
	(*RegisterServiceRequest)(nil),             // 0: auth.v1.RegisterServiceRequest
	(*RegisterServiceResponse)(nil),            // 1: auth.v1.RegisterServiceResponse
	(*ValidateServiceCredentialsRequest)(nil),  // 2: auth.v1.ValidateServiceCredentialsRequest
	(*ValidateServiceCredentialsResponse)(nil), // 3: auth.v1.ValidateServiceCredentialsResponse
	(*v1.RequestMetadata)(nil),                 // 4: common.v1.RequestMetadata
	(*v1.ResponseMetadata)(nil),                // 5: common.v1.ResponseMetadata
	(*v1.ServiceError)(nil),                    // 6: common.v1.ServiceError
	(*v1.HealthCheckRequest)(nil),              // 7: common.v1.HealthCheckRequest
	(*v1.HealthCheckResponse)(nil),             // 8: common.v1.HealthCheckResponse
}
var file_auth_v1_auth_service_proto_depIdxs = []int32{
	4, // 0: auth.v1.RegisterServiceRequest.metadata:type_name -> common.v1.RequestMetadata
	5, // 1: auth.v1.RegisterServiceResponse.metadata:type_name -> common.v1.ResponseMetadata
	6, // 2: auth.v1.RegisterServiceResponse.error:type_name -> common.v1.ServiceError
	4, // 3: auth.v1.ValidateServiceCredentialsRequest.metadata:type_name -> common.v1.RequestMetadata
	5, // 4: auth.v1.ValidateServiceCredentialsResponse.metadata:type_name -> common.v1.ResponseMetadata
	6, // 5: auth.v1.ValidateServiceCredentialsResponse.error:type_name -> common.v1.ServiceError
	0, // 6: auth.v1.AuthService.RegisterService:input_type -> auth.v1.RegisterServiceRequest
	2, // 7: auth.v1.AuthService.ValidateServiceCredentials:input_type -> auth.v1.ValidateServiceCredentialsRequest
	7, // 8: auth.v1.AuthService.HealthCheck:input_type -> common.v1.HealthCheckRequest
	1, // 9: auth.v1.AuthService.RegisterService:output_type -> auth.v1.RegisterServiceResponse
	3, // 10: auth.v1.AuthService.ValidateServiceCredentials:output_type -> auth.v1.ValidateServiceCredentialsResponse
	8, // 11: auth.v1.AuthService.HealthCheck:output_type -> common.v1.HealthCheckResponse
	9, // [9:12] is the sub-list for method output_type
	6, // [6:9] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_auth_v1_auth_service_proto_init() }
func file_auth_v1_auth_service_proto_init() {
	if File_auth_v1_auth_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_auth_v1_auth_service_proto_rawDesc), len(file_auth_v1_auth_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_auth_v1_auth_service_proto_goTypes,
		DependencyIndexes: file_auth_v1_auth_service_proto_depIdxs,
		MessageInfos:      file_auth_v1_auth_service_proto_msgTypes,
	}.Build()
	File_auth_v1_auth_service_proto = out.File
	file_auth_v1_auth_service_proto_goTypes = nil
	file_auth_v1_auth_service_proto_depIdxs = nil
}
