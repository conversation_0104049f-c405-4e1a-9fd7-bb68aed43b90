// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: common/v1/error.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ErrorDetail struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          StatusCode             `protobuf:"varint,1,opt,name=code,proto3,enum=common.v1.StatusCode" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Field         string                 `protobuf:"bytes,3,opt,name=field,proto3" json:"field,omitempty"`
	Reason        string                 `protobuf:"bytes,4,opt,name=reason,proto3" json:"reason,omitempty"`
	Domain        string                 `protobuf:"bytes,5,opt,name=domain,proto3" json:"domain,omitempty"`
	Metadata      map[string]string      `protobuf:"bytes,6,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ErrorDetail) Reset() {
	*x = ErrorDetail{}
	mi := &file_common_v1_error_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ErrorDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ErrorDetail) ProtoMessage() {}

func (x *ErrorDetail) ProtoReflect() protoreflect.Message {
	mi := &file_common_v1_error_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ErrorDetail.ProtoReflect.Descriptor instead.
func (*ErrorDetail) Descriptor() ([]byte, []int) {
	return file_common_v1_error_proto_rawDescGZIP(), []int{0}
}

func (x *ErrorDetail) GetCode() StatusCode {
	if x != nil {
		return x.Code
	}
	return StatusCode_STATUS_CODE_OK_UNSPECIFIED
}

func (x *ErrorDetail) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ErrorDetail) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *ErrorDetail) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *ErrorDetail) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *ErrorDetail) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

type ServiceError struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          StatusCode             `protobuf:"varint,1,opt,name=code,proto3,enum=common.v1.StatusCode" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Details       []*ErrorDetail         `protobuf:"bytes,3,rep,name=details,proto3" json:"details,omitempty"`
	TraceId       string                 `protobuf:"bytes,4,opt,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty"`
	SpanId        string                 `protobuf:"bytes,5,opt,name=span_id,json=spanId,proto3" json:"span_id,omitempty"`
	Timestamp     *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	ServiceName   string                 `protobuf:"bytes,7,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServiceError) Reset() {
	*x = ServiceError{}
	mi := &file_common_v1_error_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceError) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceError) ProtoMessage() {}

func (x *ServiceError) ProtoReflect() protoreflect.Message {
	mi := &file_common_v1_error_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceError.ProtoReflect.Descriptor instead.
func (*ServiceError) Descriptor() ([]byte, []int) {
	return file_common_v1_error_proto_rawDescGZIP(), []int{1}
}

func (x *ServiceError) GetCode() StatusCode {
	if x != nil {
		return x.Code
	}
	return StatusCode_STATUS_CODE_OK_UNSPECIFIED
}

func (x *ServiceError) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ServiceError) GetDetails() []*ErrorDetail {
	if x != nil {
		return x.Details
	}
	return nil
}

func (x *ServiceError) GetTraceId() string {
	if x != nil {
		return x.TraceId
	}
	return ""
}

func (x *ServiceError) GetSpanId() string {
	if x != nil {
		return x.SpanId
	}
	return ""
}

func (x *ServiceError) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *ServiceError) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

type ValidationError struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Field         string                 `protobuf:"bytes,1,opt,name=field,proto3" json:"field,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Code          string                 `protobuf:"bytes,3,opt,name=code,proto3" json:"code,omitempty"`
	Params        map[string]string      `protobuf:"bytes,4,rep,name=params,proto3" json:"params,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValidationError) Reset() {
	*x = ValidationError{}
	mi := &file_common_v1_error_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidationError) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidationError) ProtoMessage() {}

func (x *ValidationError) ProtoReflect() protoreflect.Message {
	mi := &file_common_v1_error_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidationError.ProtoReflect.Descriptor instead.
func (*ValidationError) Descriptor() ([]byte, []int) {
	return file_common_v1_error_proto_rawDescGZIP(), []int{2}
}

func (x *ValidationError) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *ValidationError) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ValidationError) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *ValidationError) GetParams() map[string]string {
	if x != nil {
		return x.Params
	}
	return nil
}

type BusinessError struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          string                 `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Entity        string                 `protobuf:"bytes,3,opt,name=entity,proto3" json:"entity,omitempty"`
	Operation     string                 `protobuf:"bytes,4,opt,name=operation,proto3" json:"operation,omitempty"`
	Context       map[string]string      `protobuf:"bytes,5,rep,name=context,proto3" json:"context,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BusinessError) Reset() {
	*x = BusinessError{}
	mi := &file_common_v1_error_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BusinessError) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessError) ProtoMessage() {}

func (x *BusinessError) ProtoReflect() protoreflect.Message {
	mi := &file_common_v1_error_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessError.ProtoReflect.Descriptor instead.
func (*BusinessError) Descriptor() ([]byte, []int) {
	return file_common_v1_error_proto_rawDescGZIP(), []int{3}
}

func (x *BusinessError) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *BusinessError) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *BusinessError) GetEntity() string {
	if x != nil {
		return x.Entity
	}
	return ""
}

func (x *BusinessError) GetOperation() string {
	if x != nil {
		return x.Operation
	}
	return ""
}

func (x *BusinessError) GetContext() map[string]string {
	if x != nil {
		return x.Context
	}
	return nil
}

var File_common_v1_error_proto protoreflect.FileDescriptor

const file_common_v1_error_proto_rawDesc = "" +
	"\n" +
	"\x15common/v1/error.proto\x12\tcommon.v1\x1a\x16common/v1/common.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\x97\x02\n" +
	"\vErrorDetail\x12)\n" +
	"\x04code\x18\x01 \x01(\x0e2\x15.common.v1.StatusCodeR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x14\n" +
	"\x05field\x18\x03 \x01(\tR\x05field\x12\x16\n" +
	"\x06reason\x18\x04 \x01(\tR\x06reason\x12\x16\n" +
	"\x06domain\x18\x05 \x01(\tR\x06domain\x12@\n" +
	"\bmetadata\x18\x06 \x03(\v2$.common.v1.ErrorDetail.MetadataEntryR\bmetadata\x1a;\n" +
	"\rMetadataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\x96\x02\n" +
	"\fServiceError\x12)\n" +
	"\x04code\x18\x01 \x01(\x0e2\x15.common.v1.StatusCodeR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x120\n" +
	"\adetails\x18\x03 \x03(\v2\x16.common.v1.ErrorDetailR\adetails\x12\x19\n" +
	"\btrace_id\x18\x04 \x01(\tR\atraceId\x12\x17\n" +
	"\aspan_id\x18\x05 \x01(\tR\x06spanId\x128\n" +
	"\ttimestamp\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\ttimestamp\x12!\n" +
	"\fservice_name\x18\a \x01(\tR\vserviceName\"\xd0\x01\n" +
	"\x0fValidationError\x12\x14\n" +
	"\x05field\x18\x01 \x01(\tR\x05field\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x12\n" +
	"\x04code\x18\x03 \x01(\tR\x04code\x12>\n" +
	"\x06params\x18\x04 \x03(\v2&.common.v1.ValidationError.ParamsEntryR\x06params\x1a9\n" +
	"\vParamsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xf0\x01\n" +
	"\rBusinessError\x12\x12\n" +
	"\x04code\x18\x01 \x01(\tR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x16\n" +
	"\x06entity\x18\x03 \x01(\tR\x06entity\x12\x1c\n" +
	"\toperation\x18\x04 \x01(\tR\toperation\x12?\n" +
	"\acontext\x18\x05 \x03(\v2%.common.v1.BusinessError.ContextEntryR\acontext\x1a:\n" +
	"\fContextEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01B8Z6gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/common/v1b\x06proto3"

var (
	file_common_v1_error_proto_rawDescOnce sync.Once
	file_common_v1_error_proto_rawDescData []byte
)

func file_common_v1_error_proto_rawDescGZIP() []byte {
	file_common_v1_error_proto_rawDescOnce.Do(func() {
		file_common_v1_error_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_common_v1_error_proto_rawDesc), len(file_common_v1_error_proto_rawDesc)))
	})
	return file_common_v1_error_proto_rawDescData
}

var file_common_v1_error_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_common_v1_error_proto_goTypes = []any{
	(*ErrorDetail)(nil),           // 0: common.v1.ErrorDetail
	(*ServiceError)(nil),          // 1: common.v1.ServiceError
	(*ValidationError)(nil),       // 2: common.v1.ValidationError
	(*BusinessError)(nil),         // 3: common.v1.BusinessError
	nil,                           // 4: common.v1.ErrorDetail.MetadataEntry
	nil,                           // 5: common.v1.ValidationError.ParamsEntry
	nil,                           // 6: common.v1.BusinessError.ContextEntry
	(StatusCode)(0),               // 7: common.v1.StatusCode
	(*timestamppb.Timestamp)(nil), // 8: google.protobuf.Timestamp
}
var file_common_v1_error_proto_depIdxs = []int32{
	7, // 0: common.v1.ErrorDetail.code:type_name -> common.v1.StatusCode
	4, // 1: common.v1.ErrorDetail.metadata:type_name -> common.v1.ErrorDetail.MetadataEntry
	7, // 2: common.v1.ServiceError.code:type_name -> common.v1.StatusCode
	0, // 3: common.v1.ServiceError.details:type_name -> common.v1.ErrorDetail
	8, // 4: common.v1.ServiceError.timestamp:type_name -> google.protobuf.Timestamp
	5, // 5: common.v1.ValidationError.params:type_name -> common.v1.ValidationError.ParamsEntry
	6, // 6: common.v1.BusinessError.context:type_name -> common.v1.BusinessError.ContextEntry
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_common_v1_error_proto_init() }
func file_common_v1_error_proto_init() {
	if File_common_v1_error_proto != nil {
		return
	}
	file_common_v1_common_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_common_v1_error_proto_rawDesc), len(file_common_v1_error_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_v1_error_proto_goTypes,
		DependencyIndexes: file_common_v1_error_proto_depIdxs,
		MessageInfos:      file_common_v1_error_proto_msgTypes,
	}.Build()
	File_common_v1_error_proto = out.File
	file_common_v1_error_proto_goTypes = nil
	file_common_v1_error_proto_depIdxs = nil
}
