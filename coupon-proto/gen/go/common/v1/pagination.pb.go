// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: common/v1/pagination.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PaginationRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	SortBy        string                 `protobuf:"bytes,3,opt,name=sort_by,json=sortBy,proto3" json:"sort_by,omitempty"`
	SortOrder     string                 `protobuf:"bytes,4,opt,name=sort_order,json=sortOrder,proto3" json:"sort_order,omitempty"`
	Cursor        string                 `protobuf:"bytes,5,opt,name=cursor,proto3" json:"cursor,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PaginationRequest) Reset() {
	*x = PaginationRequest{}
	mi := &file_common_v1_pagination_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PaginationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaginationRequest) ProtoMessage() {}

func (x *PaginationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_common_v1_pagination_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaginationRequest.ProtoReflect.Descriptor instead.
func (*PaginationRequest) Descriptor() ([]byte, []int) {
	return file_common_v1_pagination_proto_rawDescGZIP(), []int{0}
}

func (x *PaginationRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *PaginationRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *PaginationRequest) GetSortBy() string {
	if x != nil {
		return x.SortBy
	}
	return ""
}

func (x *PaginationRequest) GetSortOrder() string {
	if x != nil {
		return x.SortOrder
	}
	return ""
}

func (x *PaginationRequest) GetCursor() string {
	if x != nil {
		return x.Cursor
	}
	return ""
}

type PaginationResponse struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	CurrentPage    int32                  `protobuf:"varint,1,opt,name=current_page,json=currentPage,proto3" json:"current_page,omitempty"`
	PageSize       int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	TotalItems     int64                  `protobuf:"varint,3,opt,name=total_items,json=totalItems,proto3" json:"total_items,omitempty"`
	TotalPages     int32                  `protobuf:"varint,4,opt,name=total_pages,json=totalPages,proto3" json:"total_pages,omitempty"`
	HasNext        bool                   `protobuf:"varint,5,opt,name=has_next,json=hasNext,proto3" json:"has_next,omitempty"`
	HasPrevious    bool                   `protobuf:"varint,6,opt,name=has_previous,json=hasPrevious,proto3" json:"has_previous,omitempty"`
	NextCursor     string                 `protobuf:"bytes,7,opt,name=next_cursor,json=nextCursor,proto3" json:"next_cursor,omitempty"`
	PreviousCursor string                 `protobuf:"bytes,8,opt,name=previous_cursor,json=previousCursor,proto3" json:"previous_cursor,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *PaginationResponse) Reset() {
	*x = PaginationResponse{}
	mi := &file_common_v1_pagination_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PaginationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaginationResponse) ProtoMessage() {}

func (x *PaginationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_common_v1_pagination_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaginationResponse.ProtoReflect.Descriptor instead.
func (*PaginationResponse) Descriptor() ([]byte, []int) {
	return file_common_v1_pagination_proto_rawDescGZIP(), []int{1}
}

func (x *PaginationResponse) GetCurrentPage() int32 {
	if x != nil {
		return x.CurrentPage
	}
	return 0
}

func (x *PaginationResponse) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *PaginationResponse) GetTotalItems() int64 {
	if x != nil {
		return x.TotalItems
	}
	return 0
}

func (x *PaginationResponse) GetTotalPages() int32 {
	if x != nil {
		return x.TotalPages
	}
	return 0
}

func (x *PaginationResponse) GetHasNext() bool {
	if x != nil {
		return x.HasNext
	}
	return false
}

func (x *PaginationResponse) GetHasPrevious() bool {
	if x != nil {
		return x.HasPrevious
	}
	return false
}

func (x *PaginationResponse) GetNextCursor() string {
	if x != nil {
		return x.NextCursor
	}
	return ""
}

func (x *PaginationResponse) GetPreviousCursor() string {
	if x != nil {
		return x.PreviousCursor
	}
	return ""
}

type FilterRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Conditions    []*FilterCondition     `protobuf:"bytes,1,rep,name=conditions,proto3" json:"conditions,omitempty"`
	LogicOperator string                 `protobuf:"bytes,2,opt,name=logic_operator,json=logicOperator,proto3" json:"logic_operator,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FilterRequest) Reset() {
	*x = FilterRequest{}
	mi := &file_common_v1_pagination_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FilterRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterRequest) ProtoMessage() {}

func (x *FilterRequest) ProtoReflect() protoreflect.Message {
	mi := &file_common_v1_pagination_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterRequest.ProtoReflect.Descriptor instead.
func (*FilterRequest) Descriptor() ([]byte, []int) {
	return file_common_v1_pagination_proto_rawDescGZIP(), []int{2}
}

func (x *FilterRequest) GetConditions() []*FilterCondition {
	if x != nil {
		return x.Conditions
	}
	return nil
}

func (x *FilterRequest) GetLogicOperator() string {
	if x != nil {
		return x.LogicOperator
	}
	return ""
}

type FilterCondition struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Field         string                 `protobuf:"bytes,1,opt,name=field,proto3" json:"field,omitempty"`
	Operator      string                 `protobuf:"bytes,2,opt,name=operator,proto3" json:"operator,omitempty"`
	Values        []string               `protobuf:"bytes,3,rep,name=values,proto3" json:"values,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FilterCondition) Reset() {
	*x = FilterCondition{}
	mi := &file_common_v1_pagination_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FilterCondition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterCondition) ProtoMessage() {}

func (x *FilterCondition) ProtoReflect() protoreflect.Message {
	mi := &file_common_v1_pagination_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterCondition.ProtoReflect.Descriptor instead.
func (*FilterCondition) Descriptor() ([]byte, []int) {
	return file_common_v1_pagination_proto_rawDescGZIP(), []int{3}
}

func (x *FilterCondition) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *FilterCondition) GetOperator() string {
	if x != nil {
		return x.Operator
	}
	return ""
}

func (x *FilterCondition) GetValues() []string {
	if x != nil {
		return x.Values
	}
	return nil
}

var File_common_v1_pagination_proto protoreflect.FileDescriptor

const file_common_v1_pagination_proto_rawDesc = "" +
	"\n" +
	"\x1acommon/v1/pagination.proto\x12\tcommon.v1\"\x94\x01\n" +
	"\x11PaginationRequest\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x02 \x01(\x05R\bpageSize\x12\x17\n" +
	"\asort_by\x18\x03 \x01(\tR\x06sortBy\x12\x1d\n" +
	"\n" +
	"sort_order\x18\x04 \x01(\tR\tsortOrder\x12\x16\n" +
	"\x06cursor\x18\x05 \x01(\tR\x06cursor\"\x9e\x02\n" +
	"\x12PaginationResponse\x12!\n" +
	"\fcurrent_page\x18\x01 \x01(\x05R\vcurrentPage\x12\x1b\n" +
	"\tpage_size\x18\x02 \x01(\x05R\bpageSize\x12\x1f\n" +
	"\vtotal_items\x18\x03 \x01(\x03R\n" +
	"totalItems\x12\x1f\n" +
	"\vtotal_pages\x18\x04 \x01(\x05R\n" +
	"totalPages\x12\x19\n" +
	"\bhas_next\x18\x05 \x01(\bR\ahasNext\x12!\n" +
	"\fhas_previous\x18\x06 \x01(\bR\vhasPrevious\x12\x1f\n" +
	"\vnext_cursor\x18\a \x01(\tR\n" +
	"nextCursor\x12'\n" +
	"\x0fprevious_cursor\x18\b \x01(\tR\x0epreviousCursor\"r\n" +
	"\rFilterRequest\x12:\n" +
	"\n" +
	"conditions\x18\x01 \x03(\v2\x1a.common.v1.FilterConditionR\n" +
	"conditions\x12%\n" +
	"\x0elogic_operator\x18\x02 \x01(\tR\rlogicOperator\"[\n" +
	"\x0fFilterCondition\x12\x14\n" +
	"\x05field\x18\x01 \x01(\tR\x05field\x12\x1a\n" +
	"\boperator\x18\x02 \x01(\tR\boperator\x12\x16\n" +
	"\x06values\x18\x03 \x03(\tR\x06valuesB8Z6gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/common/v1b\x06proto3"

var (
	file_common_v1_pagination_proto_rawDescOnce sync.Once
	file_common_v1_pagination_proto_rawDescData []byte
)

func file_common_v1_pagination_proto_rawDescGZIP() []byte {
	file_common_v1_pagination_proto_rawDescOnce.Do(func() {
		file_common_v1_pagination_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_common_v1_pagination_proto_rawDesc), len(file_common_v1_pagination_proto_rawDesc)))
	})
	return file_common_v1_pagination_proto_rawDescData
}

var file_common_v1_pagination_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_common_v1_pagination_proto_goTypes = []any{
	(*PaginationRequest)(nil),  // 0: common.v1.PaginationRequest
	(*PaginationResponse)(nil), // 1: common.v1.PaginationResponse
	(*FilterRequest)(nil),      // 2: common.v1.FilterRequest
	(*FilterCondition)(nil),    // 3: common.v1.FilterCondition
}
var file_common_v1_pagination_proto_depIdxs = []int32{
	3, // 0: common.v1.FilterRequest.conditions:type_name -> common.v1.FilterCondition
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_common_v1_pagination_proto_init() }
func file_common_v1_pagination_proto_init() {
	if File_common_v1_pagination_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_common_v1_pagination_proto_rawDesc), len(file_common_v1_pagination_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_v1_pagination_proto_goTypes,
		DependencyIndexes: file_common_v1_pagination_proto_depIdxs,
		MessageInfos:      file_common_v1_pagination_proto_msgTypes,
	}.Build()
	File_common_v1_pagination_proto = out.File
	file_common_v1_pagination_proto_goTypes = nil
	file_common_v1_pagination_proto_depIdxs = nil
}
