// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: common/v1/common.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type StatusCode int32

const (
	StatusCode_STATUS_CODE_OK_UNSPECIFIED      StatusCode = 0
	StatusCode_STATUS_CODE_CANCELLED           StatusCode = 1
	StatusCode_STATUS_CODE_UNKNOWN             StatusCode = 2
	StatusCode_STATUS_CODE_INVALID_ARGUMENT    StatusCode = 3
	StatusCode_STATUS_CODE_DEADLINE_EXCEEDED   StatusCode = 4
	StatusCode_STATUS_CODE_NOT_FOUND           StatusCode = 5
	StatusCode_STATUS_CODE_ALREADY_EXISTS      StatusCode = 6
	StatusCode_STATUS_CODE_PERMISSION_DENIED   StatusCode = 7
	StatusCode_STATUS_CODE_RESOURCE_EXHAUSTED  StatusCode = 8
	StatusCode_STATUS_CODE_FAILED_PRECONDITION StatusCode = 9
	StatusCode_STATUS_CODE_ABORTED             StatusCode = 10
	StatusCode_STATUS_CODE_OUT_OF_RANGE        StatusCode = 11
	StatusCode_STATUS_CODE_UNIMPLEMENTED       StatusCode = 12
	StatusCode_STATUS_CODE_INTERNAL            StatusCode = 13
	StatusCode_STATUS_CODE_UNAVAILABLE         StatusCode = 14
	StatusCode_STATUS_CODE_DATA_LOSS           StatusCode = 15
	StatusCode_STATUS_CODE_UNAUTHENTICATED     StatusCode = 16
)

// Enum value maps for StatusCode.
var (
	StatusCode_name = map[int32]string{
		0:  "STATUS_CODE_OK_UNSPECIFIED",
		1:  "STATUS_CODE_CANCELLED",
		2:  "STATUS_CODE_UNKNOWN",
		3:  "STATUS_CODE_INVALID_ARGUMENT",
		4:  "STATUS_CODE_DEADLINE_EXCEEDED",
		5:  "STATUS_CODE_NOT_FOUND",
		6:  "STATUS_CODE_ALREADY_EXISTS",
		7:  "STATUS_CODE_PERMISSION_DENIED",
		8:  "STATUS_CODE_RESOURCE_EXHAUSTED",
		9:  "STATUS_CODE_FAILED_PRECONDITION",
		10: "STATUS_CODE_ABORTED",
		11: "STATUS_CODE_OUT_OF_RANGE",
		12: "STATUS_CODE_UNIMPLEMENTED",
		13: "STATUS_CODE_INTERNAL",
		14: "STATUS_CODE_UNAVAILABLE",
		15: "STATUS_CODE_DATA_LOSS",
		16: "STATUS_CODE_UNAUTHENTICATED",
	}
	StatusCode_value = map[string]int32{
		"STATUS_CODE_OK_UNSPECIFIED":      0,
		"STATUS_CODE_CANCELLED":           1,
		"STATUS_CODE_UNKNOWN":             2,
		"STATUS_CODE_INVALID_ARGUMENT":    3,
		"STATUS_CODE_DEADLINE_EXCEEDED":   4,
		"STATUS_CODE_NOT_FOUND":           5,
		"STATUS_CODE_ALREADY_EXISTS":      6,
		"STATUS_CODE_PERMISSION_DENIED":   7,
		"STATUS_CODE_RESOURCE_EXHAUSTED":  8,
		"STATUS_CODE_FAILED_PRECONDITION": 9,
		"STATUS_CODE_ABORTED":             10,
		"STATUS_CODE_OUT_OF_RANGE":        11,
		"STATUS_CODE_UNIMPLEMENTED":       12,
		"STATUS_CODE_INTERNAL":            13,
		"STATUS_CODE_UNAVAILABLE":         14,
		"STATUS_CODE_DATA_LOSS":           15,
		"STATUS_CODE_UNAUTHENTICATED":     16,
	}
)

func (x StatusCode) Enum() *StatusCode {
	p := new(StatusCode)
	*p = x
	return p
}

func (x StatusCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StatusCode) Descriptor() protoreflect.EnumDescriptor {
	return file_common_v1_common_proto_enumTypes[0].Descriptor()
}

func (StatusCode) Type() protoreflect.EnumType {
	return &file_common_v1_common_proto_enumTypes[0]
}

func (x StatusCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StatusCode.Descriptor instead.
func (StatusCode) EnumDescriptor() ([]byte, []int) {
	return file_common_v1_common_proto_rawDescGZIP(), []int{0}
}

type HealthCheckResponse_ServingStatus int32

const (
	HealthCheckResponse_SERVING_STATUS_UNSPECIFIED     HealthCheckResponse_ServingStatus = 0
	HealthCheckResponse_SERVING_STATUS_SERVING         HealthCheckResponse_ServingStatus = 1
	HealthCheckResponse_SERVING_STATUS_NOT_SERVING     HealthCheckResponse_ServingStatus = 2
	HealthCheckResponse_SERVING_STATUS_SERVICE_UNKNOWN HealthCheckResponse_ServingStatus = 3
)

// Enum value maps for HealthCheckResponse_ServingStatus.
var (
	HealthCheckResponse_ServingStatus_name = map[int32]string{
		0: "SERVING_STATUS_UNSPECIFIED",
		1: "SERVING_STATUS_SERVING",
		2: "SERVING_STATUS_NOT_SERVING",
		3: "SERVING_STATUS_SERVICE_UNKNOWN",
	}
	HealthCheckResponse_ServingStatus_value = map[string]int32{
		"SERVING_STATUS_UNSPECIFIED":     0,
		"SERVING_STATUS_SERVING":         1,
		"SERVING_STATUS_NOT_SERVING":     2,
		"SERVING_STATUS_SERVICE_UNKNOWN": 3,
	}
)

func (x HealthCheckResponse_ServingStatus) Enum() *HealthCheckResponse_ServingStatus {
	p := new(HealthCheckResponse_ServingStatus)
	*p = x
	return p
}

func (x HealthCheckResponse_ServingStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HealthCheckResponse_ServingStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_common_v1_common_proto_enumTypes[1].Descriptor()
}

func (HealthCheckResponse_ServingStatus) Type() protoreflect.EnumType {
	return &file_common_v1_common_proto_enumTypes[1]
}

func (x HealthCheckResponse_ServingStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HealthCheckResponse_ServingStatus.Descriptor instead.
func (HealthCheckResponse_ServingStatus) EnumDescriptor() ([]byte, []int) {
	return file_common_v1_common_proto_rawDescGZIP(), []int{3, 0}
}

type RequestMetadata struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	RequestId      string                 `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	CorrelationId  string                 `protobuf:"bytes,2,opt,name=correlation_id,json=correlationId,proto3" json:"correlation_id,omitempty"`
	ClientId       string                 `protobuf:"bytes,3,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	ClientKey      string                 `protobuf:"bytes,4,opt,name=client_key,json=clientKey,proto3" json:"client_key,omitempty"`
	UserId         uint64                 `protobuf:"varint,5,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Roles          []string               `protobuf:"bytes,6,rep,name=roles,proto3" json:"roles,omitempty"`
	Timestamp      *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	ServiceName    string                 `protobuf:"bytes,8,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	ServiceVersion string                 `protobuf:"bytes,9,opt,name=service_version,json=serviceVersion,proto3" json:"service_version,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *RequestMetadata) Reset() {
	*x = RequestMetadata{}
	mi := &file_common_v1_common_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RequestMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestMetadata) ProtoMessage() {}

func (x *RequestMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_common_v1_common_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestMetadata.ProtoReflect.Descriptor instead.
func (*RequestMetadata) Descriptor() ([]byte, []int) {
	return file_common_v1_common_proto_rawDescGZIP(), []int{0}
}

func (x *RequestMetadata) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *RequestMetadata) GetCorrelationId() string {
	if x != nil {
		return x.CorrelationId
	}
	return ""
}

func (x *RequestMetadata) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *RequestMetadata) GetClientKey() string {
	if x != nil {
		return x.ClientKey
	}
	return ""
}

func (x *RequestMetadata) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *RequestMetadata) GetRoles() []string {
	if x != nil {
		return x.Roles
	}
	return nil
}

func (x *RequestMetadata) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *RequestMetadata) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *RequestMetadata) GetServiceVersion() string {
	if x != nil {
		return x.ServiceVersion
	}
	return ""
}

type ResponseMetadata struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	RequestId        string                 `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	CorrelationId    string                 `protobuf:"bytes,2,opt,name=correlation_id,json=correlationId,proto3" json:"correlation_id,omitempty"`
	Timestamp        *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	ServiceName      string                 `protobuf:"bytes,4,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	ServiceVersion   string                 `protobuf:"bytes,5,opt,name=service_version,json=serviceVersion,proto3" json:"service_version,omitempty"`
	ProcessingTimeMs int64                  `protobuf:"varint,6,opt,name=processing_time_ms,json=processingTimeMs,proto3" json:"processing_time_ms,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *ResponseMetadata) Reset() {
	*x = ResponseMetadata{}
	mi := &file_common_v1_common_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResponseMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResponseMetadata) ProtoMessage() {}

func (x *ResponseMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_common_v1_common_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResponseMetadata.ProtoReflect.Descriptor instead.
func (*ResponseMetadata) Descriptor() ([]byte, []int) {
	return file_common_v1_common_proto_rawDescGZIP(), []int{1}
}

func (x *ResponseMetadata) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *ResponseMetadata) GetCorrelationId() string {
	if x != nil {
		return x.CorrelationId
	}
	return ""
}

func (x *ResponseMetadata) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *ResponseMetadata) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *ResponseMetadata) GetServiceVersion() string {
	if x != nil {
		return x.ServiceVersion
	}
	return ""
}

func (x *ResponseMetadata) GetProcessingTimeMs() int64 {
	if x != nil {
		return x.ProcessingTimeMs
	}
	return 0
}

type HealthCheckRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Service       string                 `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckRequest) Reset() {
	*x = HealthCheckRequest{}
	mi := &file_common_v1_common_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckRequest) ProtoMessage() {}

func (x *HealthCheckRequest) ProtoReflect() protoreflect.Message {
	mi := &file_common_v1_common_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckRequest.ProtoReflect.Descriptor instead.
func (*HealthCheckRequest) Descriptor() ([]byte, []int) {
	return file_common_v1_common_proto_rawDescGZIP(), []int{2}
}

func (x *HealthCheckRequest) GetService() string {
	if x != nil {
		return x.Service
	}
	return ""
}

type HealthCheckResponse struct {
	state         protoimpl.MessageState            `protogen:"open.v1"`
	Status        HealthCheckResponse_ServingStatus `protobuf:"varint,1,opt,name=status,proto3,enum=common.v1.HealthCheckResponse_ServingStatus" json:"status,omitempty"`
	Message       string                            `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Timestamp     *timestamppb.Timestamp            `protobuf:"bytes,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckResponse) Reset() {
	*x = HealthCheckResponse{}
	mi := &file_common_v1_common_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckResponse) ProtoMessage() {}

func (x *HealthCheckResponse) ProtoReflect() protoreflect.Message {
	mi := &file_common_v1_common_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckResponse.ProtoReflect.Descriptor instead.
func (*HealthCheckResponse) Descriptor() ([]byte, []int) {
	return file_common_v1_common_proto_rawDescGZIP(), []int{3}
}

func (x *HealthCheckResponse) GetStatus() HealthCheckResponse_ServingStatus {
	if x != nil {
		return x.Status
	}
	return HealthCheckResponse_SERVING_STATUS_UNSPECIFIED
}

func (x *HealthCheckResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *HealthCheckResponse) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

type AuditInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CreatedBy     uint64                 `protobuf:"varint,1,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedBy     uint64                 `protobuf:"varint,3,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	Version       int64                  `protobuf:"varint,5,opt,name=version,proto3" json:"version,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AuditInfo) Reset() {
	*x = AuditInfo{}
	mi := &file_common_v1_common_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AuditInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuditInfo) ProtoMessage() {}

func (x *AuditInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_v1_common_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuditInfo.ProtoReflect.Descriptor instead.
func (*AuditInfo) Descriptor() ([]byte, []int) {
	return file_common_v1_common_proto_rawDescGZIP(), []int{4}
}

func (x *AuditInfo) GetCreatedBy() uint64 {
	if x != nil {
		return x.CreatedBy
	}
	return 0
}

func (x *AuditInfo) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *AuditInfo) GetUpdatedBy() uint64 {
	if x != nil {
		return x.UpdatedBy
	}
	return 0
}

func (x *AuditInfo) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *AuditInfo) GetVersion() int64 {
	if x != nil {
		return x.Version
	}
	return 0
}

var File_common_v1_common_proto protoreflect.FileDescriptor

const file_common_v1_common_proto_rawDesc = "" +
	"\n" +
	"\x16common/v1/common.proto\x12\tcommon.v1\x1a\x1fgoogle/protobuf/timestamp.proto\"\xc8\x02\n" +
	"\x0fRequestMetadata\x12\x1d\n" +
	"\n" +
	"request_id\x18\x01 \x01(\tR\trequestId\x12%\n" +
	"\x0ecorrelation_id\x18\x02 \x01(\tR\rcorrelationId\x12\x1b\n" +
	"\tclient_id\x18\x03 \x01(\tR\bclientId\x12\x1d\n" +
	"\n" +
	"client_key\x18\x04 \x01(\tR\tclientKey\x12\x17\n" +
	"\auser_id\x18\x05 \x01(\x04R\x06userId\x12\x14\n" +
	"\x05roles\x18\x06 \x03(\tR\x05roles\x128\n" +
	"\ttimestamp\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\ttimestamp\x12!\n" +
	"\fservice_name\x18\b \x01(\tR\vserviceName\x12'\n" +
	"\x0fservice_version\x18\t \x01(\tR\x0eserviceVersion\"\x8c\x02\n" +
	"\x10ResponseMetadata\x12\x1d\n" +
	"\n" +
	"request_id\x18\x01 \x01(\tR\trequestId\x12%\n" +
	"\x0ecorrelation_id\x18\x02 \x01(\tR\rcorrelationId\x128\n" +
	"\ttimestamp\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\ttimestamp\x12!\n" +
	"\fservice_name\x18\x04 \x01(\tR\vserviceName\x12'\n" +
	"\x0fservice_version\x18\x05 \x01(\tR\x0eserviceVersion\x12,\n" +
	"\x12processing_time_ms\x18\x06 \x01(\x03R\x10processingTimeMs\".\n" +
	"\x12HealthCheckRequest\x12\x18\n" +
	"\aservice\x18\x01 \x01(\tR\aservice\"\xc1\x02\n" +
	"\x13HealthCheckResponse\x12D\n" +
	"\x06status\x18\x01 \x01(\x0e2,.common.v1.HealthCheckResponse.ServingStatusR\x06status\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x128\n" +
	"\ttimestamp\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\ttimestamp\"\x8f\x01\n" +
	"\rServingStatus\x12\x1e\n" +
	"\x1aSERVING_STATUS_UNSPECIFIED\x10\x00\x12\x1a\n" +
	"\x16SERVING_STATUS_SERVING\x10\x01\x12\x1e\n" +
	"\x1aSERVING_STATUS_NOT_SERVING\x10\x02\x12\"\n" +
	"\x1eSERVING_STATUS_SERVICE_UNKNOWN\x10\x03\"\xd9\x01\n" +
	"\tAuditInfo\x12\x1d\n" +
	"\n" +
	"created_by\x18\x01 \x01(\x04R\tcreatedBy\x129\n" +
	"\n" +
	"created_at\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x12\x1d\n" +
	"\n" +
	"updated_by\x18\x03 \x01(\x04R\tupdatedBy\x129\n" +
	"\n" +
	"updated_at\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\x12\x18\n" +
	"\aversion\x18\x05 \x01(\x03R\aversion*\x95\x04\n" +
	"\n" +
	"StatusCode\x12\x1e\n" +
	"\x1aSTATUS_CODE_OK_UNSPECIFIED\x10\x00\x12\x19\n" +
	"\x15STATUS_CODE_CANCELLED\x10\x01\x12\x17\n" +
	"\x13STATUS_CODE_UNKNOWN\x10\x02\x12 \n" +
	"\x1cSTATUS_CODE_INVALID_ARGUMENT\x10\x03\x12!\n" +
	"\x1dSTATUS_CODE_DEADLINE_EXCEEDED\x10\x04\x12\x19\n" +
	"\x15STATUS_CODE_NOT_FOUND\x10\x05\x12\x1e\n" +
	"\x1aSTATUS_CODE_ALREADY_EXISTS\x10\x06\x12!\n" +
	"\x1dSTATUS_CODE_PERMISSION_DENIED\x10\a\x12\"\n" +
	"\x1eSTATUS_CODE_RESOURCE_EXHAUSTED\x10\b\x12#\n" +
	"\x1fSTATUS_CODE_FAILED_PRECONDITION\x10\t\x12\x17\n" +
	"\x13STATUS_CODE_ABORTED\x10\n" +
	"\x12\x1c\n" +
	"\x18STATUS_CODE_OUT_OF_RANGE\x10\v\x12\x1d\n" +
	"\x19STATUS_CODE_UNIMPLEMENTED\x10\f\x12\x18\n" +
	"\x14STATUS_CODE_INTERNAL\x10\r\x12\x1b\n" +
	"\x17STATUS_CODE_UNAVAILABLE\x10\x0e\x12\x19\n" +
	"\x15STATUS_CODE_DATA_LOSS\x10\x0f\x12\x1f\n" +
	"\x1bSTATUS_CODE_UNAUTHENTICATED\x10\x10B8Z6gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/common/v1b\x06proto3"

var (
	file_common_v1_common_proto_rawDescOnce sync.Once
	file_common_v1_common_proto_rawDescData []byte
)

func file_common_v1_common_proto_rawDescGZIP() []byte {
	file_common_v1_common_proto_rawDescOnce.Do(func() {
		file_common_v1_common_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_common_v1_common_proto_rawDesc), len(file_common_v1_common_proto_rawDesc)))
	})
	return file_common_v1_common_proto_rawDescData
}

var file_common_v1_common_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_common_v1_common_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_common_v1_common_proto_goTypes = []any{
	(StatusCode)(0),                        // 0: common.v1.StatusCode
	(HealthCheckResponse_ServingStatus)(0), // 1: common.v1.HealthCheckResponse.ServingStatus
	(*RequestMetadata)(nil),                // 2: common.v1.RequestMetadata
	(*ResponseMetadata)(nil),               // 3: common.v1.ResponseMetadata
	(*HealthCheckRequest)(nil),             // 4: common.v1.HealthCheckRequest
	(*HealthCheckResponse)(nil),            // 5: common.v1.HealthCheckResponse
	(*AuditInfo)(nil),                      // 6: common.v1.AuditInfo
	(*timestamppb.Timestamp)(nil),          // 7: google.protobuf.Timestamp
}
var file_common_v1_common_proto_depIdxs = []int32{
	7, // 0: common.v1.RequestMetadata.timestamp:type_name -> google.protobuf.Timestamp
	7, // 1: common.v1.ResponseMetadata.timestamp:type_name -> google.protobuf.Timestamp
	1, // 2: common.v1.HealthCheckResponse.status:type_name -> common.v1.HealthCheckResponse.ServingStatus
	7, // 3: common.v1.HealthCheckResponse.timestamp:type_name -> google.protobuf.Timestamp
	7, // 4: common.v1.AuditInfo.created_at:type_name -> google.protobuf.Timestamp
	7, // 5: common.v1.AuditInfo.updated_at:type_name -> google.protobuf.Timestamp
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_common_v1_common_proto_init() }
func file_common_v1_common_proto_init() {
	if File_common_v1_common_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_common_v1_common_proto_rawDesc), len(file_common_v1_common_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_v1_common_proto_goTypes,
		DependencyIndexes: file_common_v1_common_proto_depIdxs,
		EnumInfos:         file_common_v1_common_proto_enumTypes,
		MessageInfos:      file_common_v1_common_proto_msgTypes,
	}.Build()
	File_common_v1_common_proto = out.File
	file_common_v1_common_proto_goTypes = nil
	file_common_v1_common_proto_depIdxs = nil
}
