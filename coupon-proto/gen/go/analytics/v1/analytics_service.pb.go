// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: analytics/v1/analytics_service.proto

package v1

import (
	v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/common/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Event struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	EventType     string                 `protobuf:"bytes,3,opt,name=event_type,json=eventType,proto3" json:"event_type,omitempty"`
	Timestamp     *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Properties    map[string]string      `protobuf:"bytes,5,rep,name=properties,proto3" json:"properties,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Event) Reset() {
	*x = Event{}
	mi := &file_analytics_v1_analytics_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Event) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Event) ProtoMessage() {}

func (x *Event) ProtoReflect() protoreflect.Message {
	mi := &file_analytics_v1_analytics_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Event.ProtoReflect.Descriptor instead.
func (*Event) Descriptor() ([]byte, []int) {
	return file_analytics_v1_analytics_service_proto_rawDescGZIP(), []int{0}
}

func (x *Event) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Event) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *Event) GetEventType() string {
	if x != nil {
		return x.EventType
	}
	return ""
}

func (x *Event) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *Event) GetProperties() map[string]string {
	if x != nil {
		return x.Properties
	}
	return nil
}

type TrackEventRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Metadata       *v1.RequestMetadata    `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	UserId         string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	EventType      string                 `protobuf:"bytes,3,opt,name=event_type,json=eventType,proto3" json:"event_type,omitempty"`
	Properties     map[string]string      `protobuf:"bytes,4,rep,name=properties,proto3" json:"properties,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	EventTimestamp *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=event_timestamp,json=eventTimestamp,proto3" json:"event_timestamp,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *TrackEventRequest) Reset() {
	*x = TrackEventRequest{}
	mi := &file_analytics_v1_analytics_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrackEventRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrackEventRequest) ProtoMessage() {}

func (x *TrackEventRequest) ProtoReflect() protoreflect.Message {
	mi := &file_analytics_v1_analytics_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrackEventRequest.ProtoReflect.Descriptor instead.
func (*TrackEventRequest) Descriptor() ([]byte, []int) {
	return file_analytics_v1_analytics_service_proto_rawDescGZIP(), []int{1}
}

func (x *TrackEventRequest) GetMetadata() *v1.RequestMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *TrackEventRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *TrackEventRequest) GetEventType() string {
	if x != nil {
		return x.EventType
	}
	return ""
}

func (x *TrackEventRequest) GetProperties() map[string]string {
	if x != nil {
		return x.Properties
	}
	return nil
}

func (x *TrackEventRequest) GetEventTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.EventTimestamp
	}
	return nil
}

type TrackEventResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.ResponseMetadata   `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Event         *Event                 `protobuf:"bytes,2,opt,name=event,proto3" json:"event,omitempty"`
	Error         *v1.ServiceError       `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrackEventResponse) Reset() {
	*x = TrackEventResponse{}
	mi := &file_analytics_v1_analytics_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrackEventResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrackEventResponse) ProtoMessage() {}

func (x *TrackEventResponse) ProtoReflect() protoreflect.Message {
	mi := &file_analytics_v1_analytics_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrackEventResponse.ProtoReflect.Descriptor instead.
func (*TrackEventResponse) Descriptor() ([]byte, []int) {
	return file_analytics_v1_analytics_service_proto_rawDescGZIP(), []int{2}
}

func (x *TrackEventResponse) GetMetadata() *v1.ResponseMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *TrackEventResponse) GetEvent() *Event {
	if x != nil {
		return x.Event
	}
	return nil
}

func (x *TrackEventResponse) GetError() *v1.ServiceError {
	if x != nil {
		return x.Error
	}
	return nil
}

type GetUserActivityRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.RequestMetadata    `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	StartTime     *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime       *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserActivityRequest) Reset() {
	*x = GetUserActivityRequest{}
	mi := &file_analytics_v1_analytics_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserActivityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserActivityRequest) ProtoMessage() {}

func (x *GetUserActivityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_analytics_v1_analytics_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserActivityRequest.ProtoReflect.Descriptor instead.
func (*GetUserActivityRequest) Descriptor() ([]byte, []int) {
	return file_analytics_v1_analytics_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetUserActivityRequest) GetMetadata() *v1.RequestMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *GetUserActivityRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetUserActivityRequest) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *GetUserActivityRequest) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

type GetUserActivityResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.ResponseMetadata   `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Events        []*Event               `protobuf:"bytes,2,rep,name=events,proto3" json:"events,omitempty"`
	Error         *v1.ServiceError       `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserActivityResponse) Reset() {
	*x = GetUserActivityResponse{}
	mi := &file_analytics_v1_analytics_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserActivityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserActivityResponse) ProtoMessage() {}

func (x *GetUserActivityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_analytics_v1_analytics_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserActivityResponse.ProtoReflect.Descriptor instead.
func (*GetUserActivityResponse) Descriptor() ([]byte, []int) {
	return file_analytics_v1_analytics_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetUserActivityResponse) GetMetadata() *v1.ResponseMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *GetUserActivityResponse) GetEvents() []*Event {
	if x != nil {
		return x.Events
	}
	return nil
}

func (x *GetUserActivityResponse) GetError() *v1.ServiceError {
	if x != nil {
		return x.Error
	}
	return nil
}

var File_analytics_v1_analytics_service_proto protoreflect.FileDescriptor

const file_analytics_v1_analytics_service_proto_rawDesc = "" +
	"\n" +
	"$analytics/v1/analytics_service.proto\x12\fanalytics.v1\x1a\x16common/v1/common.proto\x1a\x15common/v1/error.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\x8d\x02\n" +
	"\x05Event\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\x12\x1d\n" +
	"\n" +
	"event_type\x18\x03 \x01(\tR\teventType\x128\n" +
	"\ttimestamp\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\ttimestamp\x12C\n" +
	"\n" +
	"properties\x18\x05 \x03(\v2#.analytics.v1.Event.PropertiesEntryR\n" +
	"properties\x1a=\n" +
	"\x0fPropertiesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xd8\x02\n" +
	"\x11TrackEventRequest\x126\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1a.common.v1.RequestMetadataR\bmetadata\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\x12\x1d\n" +
	"\n" +
	"event_type\x18\x03 \x01(\tR\teventType\x12O\n" +
	"\n" +
	"properties\x18\x04 \x03(\v2/.analytics.v1.TrackEventRequest.PropertiesEntryR\n" +
	"properties\x12C\n" +
	"\x0fevent_timestamp\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\x0eeventTimestamp\x1a=\n" +
	"\x0fPropertiesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xa7\x01\n" +
	"\x12TrackEventResponse\x127\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1b.common.v1.ResponseMetadataR\bmetadata\x12)\n" +
	"\x05event\x18\x02 \x01(\v2\x13.analytics.v1.EventR\x05event\x12-\n" +
	"\x05error\x18\x03 \x01(\v2\x17.common.v1.ServiceErrorR\x05error\"\xdb\x01\n" +
	"\x16GetUserActivityRequest\x126\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1a.common.v1.RequestMetadataR\bmetadata\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\x129\n" +
	"\n" +
	"start_time\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x125\n" +
	"\bend_time\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\aendTime\"\xae\x01\n" +
	"\x17GetUserActivityResponse\x127\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1b.common.v1.ResponseMetadataR\bmetadata\x12+\n" +
	"\x06events\x18\x02 \x03(\v2\x13.analytics.v1.EventR\x06events\x12-\n" +
	"\x05error\x18\x03 \x01(\v2\x17.common.v1.ServiceErrorR\x05error2\x91\x02\n" +
	"\x10AnalyticsService\x12O\n" +
	"\n" +
	"TrackEvent\x12\x1f.analytics.v1.TrackEventRequest\x1a .analytics.v1.TrackEventResponse\x12^\n" +
	"\x0fGetUserActivity\x12$.analytics.v1.GetUserActivityRequest\x1a%.analytics.v1.GetUserActivityResponse\x12L\n" +
	"\vHealthCheck\x12\x1d.common.v1.HealthCheckRequest\x1a\x1e.common.v1.HealthCheckResponseB;Z9gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/analytics/v1b\x06proto3"

var (
	file_analytics_v1_analytics_service_proto_rawDescOnce sync.Once
	file_analytics_v1_analytics_service_proto_rawDescData []byte
)

func file_analytics_v1_analytics_service_proto_rawDescGZIP() []byte {
	file_analytics_v1_analytics_service_proto_rawDescOnce.Do(func() {
		file_analytics_v1_analytics_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_analytics_v1_analytics_service_proto_rawDesc), len(file_analytics_v1_analytics_service_proto_rawDesc)))
	})
	return file_analytics_v1_analytics_service_proto_rawDescData
}

var file_analytics_v1_analytics_service_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_analytics_v1_analytics_service_proto_goTypes = []any{
	(*Event)(nil),                   // 0: analytics.v1.Event
	(*TrackEventRequest)(nil),       // 1: analytics.v1.TrackEventRequest
	(*TrackEventResponse)(nil),      // 2: analytics.v1.TrackEventResponse
	(*GetUserActivityRequest)(nil),  // 3: analytics.v1.GetUserActivityRequest
	(*GetUserActivityResponse)(nil), // 4: analytics.v1.GetUserActivityResponse
	nil,                             // 5: analytics.v1.Event.PropertiesEntry
	nil,                             // 6: analytics.v1.TrackEventRequest.PropertiesEntry
	(*timestamppb.Timestamp)(nil),   // 7: google.protobuf.Timestamp
	(*v1.RequestMetadata)(nil),      // 8: common.v1.RequestMetadata
	(*v1.ResponseMetadata)(nil),     // 9: common.v1.ResponseMetadata
	(*v1.ServiceError)(nil),         // 10: common.v1.ServiceError
	(*v1.HealthCheckRequest)(nil),   // 11: common.v1.HealthCheckRequest
	(*v1.HealthCheckResponse)(nil),  // 12: common.v1.HealthCheckResponse
}
var file_analytics_v1_analytics_service_proto_depIdxs = []int32{
	7,  // 0: analytics.v1.Event.timestamp:type_name -> google.protobuf.Timestamp
	5,  // 1: analytics.v1.Event.properties:type_name -> analytics.v1.Event.PropertiesEntry
	8,  // 2: analytics.v1.TrackEventRequest.metadata:type_name -> common.v1.RequestMetadata
	6,  // 3: analytics.v1.TrackEventRequest.properties:type_name -> analytics.v1.TrackEventRequest.PropertiesEntry
	7,  // 4: analytics.v1.TrackEventRequest.event_timestamp:type_name -> google.protobuf.Timestamp
	9,  // 5: analytics.v1.TrackEventResponse.metadata:type_name -> common.v1.ResponseMetadata
	0,  // 6: analytics.v1.TrackEventResponse.event:type_name -> analytics.v1.Event
	10, // 7: analytics.v1.TrackEventResponse.error:type_name -> common.v1.ServiceError
	8,  // 8: analytics.v1.GetUserActivityRequest.metadata:type_name -> common.v1.RequestMetadata
	7,  // 9: analytics.v1.GetUserActivityRequest.start_time:type_name -> google.protobuf.Timestamp
	7,  // 10: analytics.v1.GetUserActivityRequest.end_time:type_name -> google.protobuf.Timestamp
	9,  // 11: analytics.v1.GetUserActivityResponse.metadata:type_name -> common.v1.ResponseMetadata
	0,  // 12: analytics.v1.GetUserActivityResponse.events:type_name -> analytics.v1.Event
	10, // 13: analytics.v1.GetUserActivityResponse.error:type_name -> common.v1.ServiceError
	1,  // 14: analytics.v1.AnalyticsService.TrackEvent:input_type -> analytics.v1.TrackEventRequest
	3,  // 15: analytics.v1.AnalyticsService.GetUserActivity:input_type -> analytics.v1.GetUserActivityRequest
	11, // 16: analytics.v1.AnalyticsService.HealthCheck:input_type -> common.v1.HealthCheckRequest
	2,  // 17: analytics.v1.AnalyticsService.TrackEvent:output_type -> analytics.v1.TrackEventResponse
	4,  // 18: analytics.v1.AnalyticsService.GetUserActivity:output_type -> analytics.v1.GetUserActivityResponse
	12, // 19: analytics.v1.AnalyticsService.HealthCheck:output_type -> common.v1.HealthCheckResponse
	17, // [17:20] is the sub-list for method output_type
	14, // [14:17] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_analytics_v1_analytics_service_proto_init() }
func file_analytics_v1_analytics_service_proto_init() {
	if File_analytics_v1_analytics_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_analytics_v1_analytics_service_proto_rawDesc), len(file_analytics_v1_analytics_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_analytics_v1_analytics_service_proto_goTypes,
		DependencyIndexes: file_analytics_v1_analytics_service_proto_depIdxs,
		MessageInfos:      file_analytics_v1_analytics_service_proto_msgTypes,
	}.Build()
	File_analytics_v1_analytics_service_proto = out.File
	file_analytics_v1_analytics_service_proto_goTypes = nil
	file_analytics_v1_analytics_service_proto_depIdxs = nil
}
