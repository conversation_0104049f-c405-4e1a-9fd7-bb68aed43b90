#!/bin/sh

# This script installs the dependencies required for the Protobuf toolchain.
# It installs buf, protoc-gen-go, and protoc-gen-go-grpc.

set -e

echo "--- Installing Go Protobuf dependencies ---"
go install google.golang.org/protobuf/cmd/protoc-gen-go@latest
go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest

# Add GOPATH/bin to the system's PATH if it's not already there
if ! echo "$PATH" | grep -q "$GOPATH/bin"; then
  echo "Adding \$GOPATH/bin to your PATH."
  # Add to .bashrc, .zshrc, or appropriate shell profile
  # This is a generic example; users may need to adjust for their shell
  PROFILE_FILE=""
  if [ -f "$HOME/.zshrc" ]; then
    PROFILE_FILE="$HOME/.zshrc"
  elif [ -f "$HOME/.bashrc" ]; then
    PROFILE_FILE="$HOME/.bashrc"
  elif [ -f "$HOME/.profile" ]; then
    PROFILE_FILE="$HOME/.profile"
  fi

  if [ -n "$PROFILE_FILE" ]; then
    echo '' >> "$PROFILE_FILE"
    echo '# Go Path' >> "$PROFILE_FILE"
    echo 'export PATH=$PATH:$(go env GOPATH)/bin' >> "$PROFILE_FILE"
    echo "Please run 'source $PROFILE_FILE' or open a new terminal."
  else
    echo "Could not find a shell profile file (.zshrc, .bashrc, .profile)."
    echo "Please add \`export PATH=\$PATH:\$(go env GOPATH)/bin\` to your shell's configuration file manually."
  fi
fi

echo "--- Installing Buf CLI ---"
# Check if buf is already installed
if ! command -v buf > /dev/null; then
  # Use Go to install
  brew install bufbuild/buf/buf
else
  echo "Buf is already installed."
fi

echo "--- All dependencies installed successfully ---"
