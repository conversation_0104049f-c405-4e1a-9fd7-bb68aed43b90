syntax = "proto3";

package common.v1;

option go_package = "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/common/v1";

message PaginationRequest {
  int32 page = 1;
  int32 page_size = 2;
  string sort_by = 3;
  string sort_order = 4;
  string cursor = 5;
}

message PaginationResponse {
  int32 current_page = 1;
  int32 page_size = 2;
  int64 total_items = 3;
  int32 total_pages = 4;
  bool has_next = 5;
  bool has_previous = 6;
  string next_cursor = 7;
  string previous_cursor = 8;
}

message FilterRequest {
  repeated FilterCondition conditions = 1;
  string logic_operator = 2;
}

message FilterCondition {
  string field = 1;
  string operator = 2;
  repeated string values = 3;
}
