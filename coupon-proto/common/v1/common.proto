syntax = "proto3";

package common.v1;

import "google/protobuf/timestamp.proto";

option go_package = "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/common/v1";

message RequestMetadata {
  string request_id = 1;
  string correlation_id = 2;
  string client_id = 3;
  string client_key = 4;
  uint64 user_id = 5;
  repeated string roles = 6;
  google.protobuf.Timestamp timestamp = 7;
  string service_name = 8;
  string service_version = 9;
}

message ResponseMetadata {
  string request_id = 1;
  string correlation_id = 2;
  google.protobuf.Timestamp timestamp = 3;
  string service_name = 4;
  string service_version = 5;
  int64 processing_time_ms = 6;
}

message HealthCheckRequest {
  string service = 1;
}

message HealthCheckResponse {
  enum ServingStatus {
    SERVING_STATUS_UNSPECIFIED = 0;
    SERVING_STATUS_SERVING = 1;
    SERVING_STATUS_NOT_SERVING = 2;
    SERVING_STATUS_SERVICE_UNKNOWN = 3;
  }
  ServingStatus status = 1;
  string message = 2;
  google.protobuf.Timestamp timestamp = 3;
}

enum StatusCode {
  STATUS_CODE_OK_UNSPECIFIED = 0;
  STATUS_CODE_CANCELLED = 1;
  STATUS_CODE_UNKNOWN = 2;
  STATUS_CODE_INVALID_ARGUMENT = 3;
  STATUS_CODE_DEADLINE_EXCEEDED = 4;
  STATUS_CODE_NOT_FOUND = 5;
  STATUS_CODE_ALREADY_EXISTS = 6;
  STATUS_CODE_PERMISSION_DENIED = 7;
  STATUS_CODE_RESOURCE_EXHAUSTED = 8;
  STATUS_CODE_FAILED_PRECONDITION = 9;
  STATUS_CODE_ABORTED = 10;
  STATUS_CODE_OUT_OF_RANGE = 11;
  STATUS_CODE_UNIMPLEMENTED = 12;
  STATUS_CODE_INTERNAL = 13;
  STATUS_CODE_UNAVAILABLE = 14;
  STATUS_CODE_DATA_LOSS = 15;
  STATUS_CODE_UNAUTHENTICATED = 16;
}

message AuditInfo {
  uint64 created_by = 1;
  google.protobuf.Timestamp created_at = 2;
  uint64 updated_by = 3;
  google.protobuf.Timestamp updated_at = 4;
  int64 version = 5;
}
