syntax = "proto3";

package common.v1;

import "common/v1/common.proto";
import "google/protobuf/timestamp.proto";

option go_package = "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/common/v1";

message ErrorDetail {
  StatusCode code = 1;
  string message = 2;
  string field = 3;
  string reason = 4;
  string domain = 5;
  map<string, string> metadata = 6;
}

message ServiceError {
  StatusCode code = 1;
  string message = 2;
  repeated ErrorDetail details = 3;
  string trace_id = 4;
  string span_id = 5;
  google.protobuf.Timestamp timestamp = 6;
  string service_name = 7;
}

message ValidationError {
  string field = 1;
  string message = 2;
  string code = 3;
  map<string, string> params = 4;
}

message BusinessError {
  string code = 1;
  string message = 2;
  string entity = 3;
  string operation = 4;
  map<string, string> context = 5;
}
