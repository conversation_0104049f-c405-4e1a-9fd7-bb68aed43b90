.PHONY: all generate clean lint breaking install-deps

all: generate

# Generate Go code from proto files using buf
generate:
	@echo "--- Generating Protobuf and gRPC code ---"
	@buf generate
	@echo "--- Code generation complete ---"

# Remove all generated files
clean:
	@echo "--- Cleaning generated files ---"
	@rm -rf ./gen
	@echo "--- Clean complete ---"

# Lint all proto files
lint:
	@echo "--- Linting Protobuf files ---"
	@buf lint

# Check for breaking changes against the main branch
# Ensure your local git is clean before running
breaking:
	@echo "--- Checking for breaking changes against main branch ---"
	@buf breaking --against 'https://gitlab.zalopay.vn/phunn4/coupon-proto.git#branch=master'

# Install dependencies needed to generate code
install-deps:
	@./scripts/install-deps.sh
