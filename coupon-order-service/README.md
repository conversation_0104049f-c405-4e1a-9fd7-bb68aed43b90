# Coupon Order Service

This service manages order related functionality for the coupon system, including order creation, discount calculation, and order management.

## Requirements

- [Docker](https://www.docker.com/)
- [Docker Compose](https://docs.docker.com/compose/)
- `make` (optional, for convenience commands)

## Setup

1. Copy `.env.example` to `.env` and fill in the required environment variables. The values in `config/config.yaml` may contain `${VAR_NAME}` placeholders which will be expanded from these environment variables when the service starts.

```bash
cp .env.example .env
# edit .env
```

## Using Docker Compose

To build and start the service with all dependencies run:

```bash
make compose-up
```

The service will be available on:

- HTTP: [http://localhost:8085](http://localhost:8085)
- gRPC: `localhost:50056`

To stop and remove the containers use:

```bash
make compose-down
```

## Building Manually

If you only want to build the Docker image:

```bash
make build
```

You can then run it with:

```bash
make run
```

## Project Structure

- `cmd/server` – application entry point
- `internal` – service implementation packages
  - `handler/grpc` – gRPC handlers
  - `service` – business logic
  - `repository` – data access layer
  - `model` – data models
  - `clients` – external service clients
  - `middleware` – middleware components
- `config` – configuration files

## Features

- Order creation with voucher discount calculation
- Order retrieval and listing
- Order status management
- User order count tracking
- Voucher usage count tracking
- Integration with voucher service for discount validation
- Redis caching for improved performance
- Health checks and metrics
