package repository

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"time"

	"gitlab.zalopay.vn/phunn4/coupon-order-service/internal/model"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/database"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/redis"
	"gorm.io/gorm"
)

const (
	orderCachePrefix = "order:id:"
	orderCacheTTL    = 5 * time.Minute
)

type orderInfrastructure struct {
	db     *database.DB
	redis  *redis.Client
	logger *logging.Logger
}

type OrderRepository interface {
	Create(ctx context.Context, order *model.Order) error
	CreateWithItems(ctx context.Context, order *model.Order, items []model.OrderItem) error
	GetByID(ctx context.Context, orderID uint64) (*model.Order, error)
	List(ctx context.Context, req *model.ListOrdersRequest) (*model.OrderWithPagination, error)
	ListByVoucher(ctx context.Context, req *model.ListOrdersByVoucherRequest) (*model.OrderWithPagination, error)
	GetUserOrderCount(ctx context.Context, userID uint64) (int64, error)
	GetUserVoucherUsageCount(ctx context.Context, userID, voucherID uint64) (int32, error)
	UpdateStatus(ctx context.Context, orderID uint64, status string) error
}

func NewOrderRepository(db *database.DB, redis *redis.Client, logger *logging.Logger) OrderRepository {
	return &orderInfrastructure{
		db:     db,
		redis:  redis,
		logger: logger,
	}
}

func (r *orderInfrastructure) getOrderFromCache(ctx context.Context, key string) (*model.Order, error) {
	log := r.logger.WithContext(ctx)

	val, err := r.redis.Get(ctx, key)
	if err != nil || val == "" {
		return nil, err
	}

	log.Debugf("Cache hit for key: %s", key)
	var order model.Order
	if err := json.Unmarshal([]byte(val), &order); err != nil {
		log.Errorf("Failed to unmarshal order from cache for key %s: %v", key, err)
		return nil, err
	}
	return &order, nil
}

func (r *orderInfrastructure) setOrderInCache(ctx context.Context, order *model.Order) {
	log := r.logger.WithContext(ctx)

	orderBytes, err := json.Marshal(order)
	if err != nil {
		log.Errorf("Failed to marshal order for caching (ID: %d): %v", order.ID, err)
		return
	}

	key := fmt.Sprintf("%s%d", orderCachePrefix, order.ID)
	if err := r.redis.Set(ctx, key, orderBytes, orderCacheTTL); err != nil {
		log.Errorf("Failed to set order cache for key %s: %v", key, err)
	}
}

func (r *orderInfrastructure) invalidateOrderCache(ctx context.Context, orderID uint64) {
	key := fmt.Sprintf("%s%d", orderCachePrefix, orderID)
	if err := r.redis.Del(ctx, key); err != nil {
		r.logger.WithContext(ctx).Errorf("Failed to invalidate order cache for order %d: %v", orderID, err)
	}
}

func (r *orderInfrastructure) Create(ctx context.Context, order *model.Order) error {
	if err := r.db.WithContext(ctx).Create(order).Error; err != nil {
		return err
	}
	r.invalidateOrderCache(ctx, order.ID)
	return nil
}

func (r *orderInfrastructure) CreateWithItems(ctx context.Context, order *model.Order, items []model.OrderItem) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if err := tx.Create(order).Error; err != nil {
			return err
		}

		if len(items) > 0 {
			for i := range items {
				items[i].OrderID = order.ID
				items[i].CreatedAt = time.Now()
			}

			if err := tx.Create(&items).Error; err != nil {
				return err
			}
		}

		r.invalidateOrderCache(ctx, order.ID)
		return nil
	})
}

func (r *orderInfrastructure) GetByID(ctx context.Context, orderID uint64) (*model.Order, error) {
	cacheKey := fmt.Sprintf("%s%d", orderCachePrefix, orderID)

	cachedOrder, _ := r.getOrderFromCache(ctx, cacheKey)
	if cachedOrder != nil {
		return cachedOrder, nil
	}

	var order model.Order
	err := r.db.WithContext(ctx).Preload("Items").Where("id = ?", orderID).First(&order).Error
	if err != nil {
		return nil, err
	}

	r.setOrderInCache(ctx, &order)
	return &order, nil
}

func (r *orderInfrastructure) List(ctx context.Context, req *model.ListOrdersRequest) (*model.OrderWithPagination, error) {
	query := r.db.WithContext(ctx).Model(&model.Order{})

	if req.UserID != nil {
		query = query.Where("user_id = ?", *req.UserID)
	}

	if req.Search != nil && *req.Search != "" {
		query = query.Where("calculation_status ILIKE ?", "%"+*req.Search+"%")
	}

	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}

	page := max(req.Page, 1)
	limit := req.Limit
	if limit < 1 || limit > 100 {
		limit = 10
	}

	offset := (page - 1) * limit
	totalPages := int32(math.Ceil(float64(total) / float64(limit)))

	var orders []model.Order
	err := query.Preload("Items").
		Order("created_at DESC").
		Offset(int(offset)).
		Limit(int(limit)).
		Find(&orders).Error
	if err != nil {
		return nil, err
	}

	return &model.OrderWithPagination{
		Orders:      orders,
		Total:       total,
		Page:        page,
		Limit:       limit,
		TotalPages:  totalPages,
		HasNext:     page < totalPages,
		HasPrevious: page > 1,
	}, nil
}

func (r *orderInfrastructure) ListByVoucher(ctx context.Context, req *model.ListOrdersByVoucherRequest) (*model.OrderWithPagination, error) {
	query := r.db.WithContext(ctx).Model(&model.Order{}).Where("applied_voucher_id = ?", req.VoucherID)

	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}

	page := max(req.Page, 1)
	limit := req.Limit
	if limit < 1 || limit > 100 {
		limit = 10
	}

	offset := (page - 1) * limit
	totalPages := int32(math.Ceil(float64(total) / float64(limit)))

	var orders []model.Order
	err := query.Preload("Items").
		Order("created_at DESC").
		Offset(int(offset)).
		Limit(int(limit)).
		Find(&orders).Error
	if err != nil {
		return nil, err
	}

	return &model.OrderWithPagination{
		Orders:      orders,
		Total:       total,
		Page:        page,
		Limit:       limit,
		TotalPages:  totalPages,
		HasNext:     page < totalPages,
		HasPrevious: page > 1,
	}, nil
}

func (r *orderInfrastructure) GetUserOrderCount(ctx context.Context, userID uint64) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&model.Order{}).Where("user_id = ?", userID).Count(&count).Error
	return count, err
}

func (r *orderInfrastructure) GetUserVoucherUsageCount(ctx context.Context, userID, voucherID uint64) (int32, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&model.Order{}).
		Where("user_id = ? AND applied_voucher_id = ?", userID, voucherID).
		Count(&count).Error
	return int32(count), err
}

func (r *orderInfrastructure) UpdateStatus(ctx context.Context, orderID uint64, status string) error {
	err := r.db.WithContext(ctx).Model(&model.Order{}).
		Where("id = ?", orderID).
		Updates(map[string]any{
			"calculation_status": status,
			"updated_at":         time.Now(),
		}).Error
	if err != nil {
		return err
	}

	r.invalidateOrderCache(ctx, orderID)
	return nil
}
