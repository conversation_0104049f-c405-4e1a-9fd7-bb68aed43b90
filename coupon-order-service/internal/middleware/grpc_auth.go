package middleware

import (
	"context"
	"path"
	"slices"

	"gitlab.zalopay.vn/phunn4/coupon-order-service/internal/clients"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
)

var publicMethods = map[string]bool{
	"HealthCheck": true,
}

var methodAllowList = map[string][]string{
	"api-gateway": {"CreateOrder", "GetOrder", "UpdateOrderStatus", "ListOrders", "ListOrdersByVoucher", "GetUserOrderCount", "GetUserVoucherUsageCount"},
}

func CreateServiceAuthFunc(authClient *clients.AuthClient) func(context.Context) (context.Context, error) {
	return func(ctx context.Context) (context.Context, error) {
		ts := grpc.ServerTransportStreamFromContext(ctx)
		method := ""
		if ts != nil {
			method = path.Base(ts.Method())
		}

		if publicMethods[method] {
			return ctx, nil
		}

		md, ok := metadata.FromIncomingContext(ctx)
		if !ok {
			return nil, status.Error(codes.Unauthenticated, "missing metadata")
		}

		clientIDs := md.Get("client-id")
		clientKeys := md.Get("client-key")
		if len(clientIDs) > 0 && len(clientKeys) > 0 {
			serviceName, err := authClient.ValidateServiceCredentials(ctx, clientIDs[0], clientKeys[0])
			if err != nil {
				return nil, status.Error(codes.Unauthenticated, "invalid service credentials")
			}
			allowed := methodAllowList[serviceName]
			if !slices.Contains(allowed, method) {
				return nil, status.Errorf(codes.PermissionDenied, "method %s not allowed for service %s", method, serviceName)
			}
			return ctx, nil
		}
		return nil, status.Error(codes.Unauthenticated, "authentication required - use service credentials for gRPC calls")
	}
}
