package benchmark

import (
	"context"
	"fmt"
	"os"
	"testing"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/metadata"
	"gorm.io/gorm"

	userv1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/user/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/benchmark"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/database"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/redis"
)

var (
	userGrpcSuite  *benchmark.GRPCBenchmarkSuite
	userDBSuite    *benchmark.DatabaseBenchmarkSuite
	userCacheSuite *benchmark.CacheBenchmarkSuite
	userTestDB     *database.DB
	userTestRedis  *redis.Client
	userGenerator  *benchmark.TestDataGenerator
)

func TestMain(m *testing.M) {
	if err := setupBenchmarkEnvironment(); err != nil {
		fmt.Printf("Failed to setup benchmark environment: %v\n", err)
		os.Exit(1)
	}

	code := m.Run()

	cleanupUser()

	os.Exit(code)
}

func setupBenchmarkEnvironment() error {
	cfg, err := config.Load()
	if err != nil {
		return fmt.Errorf("failed to load config: %w", err)
	}

	logger := logging.New("info", "json")
	appMetrics := metrics.New("user-service-benchmark")

	userTestDB, err = database.NewPostgresDB(&cfg.Database, logger, appMetrics)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}

	userTestRedis = redis.NewClient(&cfg.Redis, logger, appMetrics)

	grpcConfig := &benchmark.GRPCBenchmarkConfig{
		ServiceAddress: "localhost:50053",
		ServiceName:    "user-service",
		Timeout:        5 * time.Second,
	}

	userGrpcSuite, err = benchmark.NewGRPCBenchmarkSuite(grpcConfig)
	if err != nil {
		return fmt.Errorf("failed to create gRPC benchmark suite: %w", err)
	}

	dbConfig := &benchmark.DatabaseBenchmarkConfig{
		ServiceName:    "user-service",
		DatabaseType:   "postgres",
		ConnectionPool: 10,
		QueryTimeout:   5 * time.Second,
	}
	userDBSuite = benchmark.NewDatabaseBenchmarkSuite(dbConfig, userTestDB.DB)

	userCacheSuite = benchmark.NewCacheBenchmarkSuite("user-service", userTestRedis)

	userGenerator = benchmark.NewTestDataGenerator()

	return nil
}

func cleanupUser() {
	if userGrpcSuite != nil {
		userGrpcSuite.Close()
	}
	if userTestDB != nil {
		sqlDB, _ := userTestDB.DB.DB()
		sqlDB.Close()
	}
	if userTestRedis != nil {
		userTestRedis.Close()
	}
}

func BenchmarkUserService_gRPC_CreateUser(b *testing.B) {
	userGrpcSuite.BenchmarkUnaryCall(b, func(ctx context.Context, conn *grpc.ClientConn) error {
		client := userv1.NewUserServiceClient(conn)

		ctx = addUserAuthMetadata(ctx)

		userData := userGenerator.GenerateUserData()
		req := &userv1.CreateUserRequest{
			Name:     userData["username"].(string),
			Email:    userData["email"].(string),
			Password: "testpassword123",
		}

		_, err := client.CreateUser(ctx, req)
		return err
	})
}

func BenchmarkUserService_gRPC_GetUser(b *testing.B) {
	userGrpcSuite.BenchmarkUnaryCall(b, func(ctx context.Context, conn *grpc.ClientConn) error {
		client := userv1.NewUserServiceClient(conn)

		ctx = addUserAuthMetadata(ctx)

		req := &userv1.GetUserRequest{
			UserId: uint64(userGenerator.GenerateUserID()),
		}

		_, err := client.GetUser(ctx, req)
		return err
	})
}

func BenchmarkUserService_gRPC_GetUserByEmail(b *testing.B) {
	userGrpcSuite.BenchmarkUnaryCall(b, func(ctx context.Context, conn *grpc.ClientConn) error {
		client := userv1.NewUserServiceClient(conn)

		ctx = addUserAuthMetadata(ctx)

		userData := userGenerator.GenerateUserData()
		req := &userv1.GetUserByEmailRequest{
			Email: userData["email"].(string),
		}

		_, err := client.GetUserByEmail(ctx, req)
		return err
	})
}

func BenchmarkUserService_gRPC_Login(b *testing.B) {
	userGrpcSuite.BenchmarkUnaryCall(b, func(ctx context.Context, conn *grpc.ClientConn) error {
		client := userv1.NewUserServiceClient(conn)

		ctx = addUserAuthMetadata(ctx)

		userData := userGenerator.GenerateUserData()
		req := &userv1.LoginRequest{
			Email:    userData["email"].(string),
			Password: "testpassword123",
		}

		_, err := client.Login(ctx, req)
		return err
	})
}

func BenchmarkUserService_Database_Queries(b *testing.B) {
	queryBenchmarks := benchmark.NewDatabaseQueryBenchmarks(userDBSuite)
	queryBenchmarks.BenchmarkUserQueries(b)
}

func BenchmarkUserService_Database_CreateUser(b *testing.B) {
	userDBSuite.BenchmarkQuery(b, "create_user", func(ctx context.Context, db *gorm.DB) error {
		userData := userGenerator.GenerateUserData()
		user := map[string]any{
			"username":   userData["username"],
			"email":      userData["email"],
			"category":   userData["category"],
			"created_at": time.Now(),
			"updated_at": time.Now(),
		}
		return db.Table("users").Create(user).Error
	})
}

func BenchmarkUserService_Database_BulkInsertUsers(b *testing.B) {
	userDBSuite.BenchmarkBulkInsert(b, "users", 100, func() any {
		userData := userGenerator.GenerateUserData()
		return map[string]any{
			"username":   userData["username"],
			"email":      userData["email"],
			"category":   userData["category"],
			"created_at": time.Now(),
			"updated_at": time.Now(),
		}
	})
}

func BenchmarkUserService_Database_Transaction(b *testing.B) {
	userDBSuite.BenchmarkTransaction(b, "user_with_profile", func(ctx context.Context, tx *gorm.DB) error {
		userData := userGenerator.GenerateUserData()
		user := map[string]any{
			"username":   userData["username"],
			"email":      userData["email"],
			"category":   userData["category"],
			"created_at": time.Now(),
			"updated_at": time.Now(),
		}

		if err := tx.Table("users").Create(user).Error; err != nil {
			return err
		}

		profile := map[string]any{
			"user_id":    user["id"],
			"first_name": "Test",
			"last_name":  "User",
			"created_at": time.Now(),
			"updated_at": time.Now(),
		}

		return tx.Table("user_profiles").Create(profile).Error
	})
}

func BenchmarkUserService_Cache_GetUser(b *testing.B) {
	userCacheSuite.BenchmarkCacheGet(b, func(ctx context.Context, key string) error {
		_, err := userTestRedis.Get(ctx, key)
		return err
	})
}

func BenchmarkUserService_Cache_SetUser(b *testing.B) {
	userCacheSuite.BenchmarkCacheSet(b, func(ctx context.Context, key string, value any) error {
		return userTestRedis.Set(ctx, key, value, time.Hour)
	})
}

func BenchmarkUserService_ConcurrentLoad(b *testing.B) {
	scenario := &benchmark.LoadTestScenario{
		Name:        "user-service-mixed-load",
		Duration:    30 * time.Second,
		Concurrency: 10,
		RampUpTime:  5 * time.Second,
		Operations: []benchmark.LoadTestOperation{
			{
				Name:   "create_user",
				Weight: 30,
				Execute: func(ctx context.Context) error {
					ctx = addUserAuthMetadata(ctx)

					conn, err := grpc.NewClient("localhost:50053", grpc.WithTransportCredentials(insecure.NewCredentials()))
					if err != nil {
						return err
					}
					defer conn.Close()

					client := userv1.NewUserServiceClient(conn)
					userData := userGenerator.GenerateUserData()
					req := &userv1.CreateUserRequest{
						Name:     userData["username"].(string),
						Email:    userData["email"].(string),
						Password: "testpassword123",
					}
					_, err = client.CreateUser(ctx, req)
					return err
				},
			},
			{
				Name:   "get_user",
				Weight: 40,
				Execute: func(ctx context.Context) error {
					ctx = addUserAuthMetadata(ctx)

					conn, err := grpc.NewClient("localhost:50053", grpc.WithTransportCredentials(insecure.NewCredentials()))
					if err != nil {
						return err
					}
					defer conn.Close()

					client := userv1.NewUserServiceClient(conn)
					req := &userv1.GetUserRequest{
						UserId: uint64(generateUserID()),
					}
					_, err = client.GetUser(ctx, req)
					return err
				},
			},
			{
				Name:   "login",
				Weight: 30,
				Execute: func(ctx context.Context) error {
					ctx = addUserAuthMetadata(ctx)

					conn, err := grpc.NewClient("localhost:50053", grpc.WithTransportCredentials(insecure.NewCredentials()))
					if err != nil {
						return err
					}
					defer conn.Close()

					client := userv1.NewUserServiceClient(conn)
					userData := userGenerator.GenerateUserData()
					req := &userv1.LoginRequest{
						Email:    userData["email"].(string),
						Password: "testpassword123",
					}
					_, err = client.Login(ctx, req)
					return err
				},
			},
		},
	}

	runner := benchmark.NewLoadTestRunner(scenario)
	runner.RunLoadTest(b)
}

func addUserAuthMetadata(ctx context.Context) context.Context {
	md := metadata.New(map[string]string{
		"client-id":  "user-service-benchmark",
		"client-key": "benchmark-key",
	})
	return metadata.NewOutgoingContext(ctx, md)
}

func generateUserID() int {
	return userGenerator.Rand().Intn(1000) + 1
}
