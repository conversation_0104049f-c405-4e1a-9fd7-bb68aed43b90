package service

import (
	"context"
	"fmt"

	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/kafka"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-user-service/internal/model"
)

type UserEventPublisher struct {
	eventPublisher *kafka.EventPublisher
	logger         *logging.Logger
	config         *config.Config
}

func NewUserEventPublisher(cfg *config.Config, logger *logging.Logger) *UserEventPublisher {
	eventPublisher := kafka.NewEventPublisher(&cfg.Kafka, logger, cfg.Service.Name, cfg.Service.Version)

	return &UserEventPublisher{
		eventPublisher: eventPublisher,
		logger:         logger,
		config:         cfg,
	}
}

type UserCreatedEvent struct {
	UserID   uint64 `json:"user_id"`
	Email    string `json:"email"`
	Name     string `json:"name"`
	UserType string `json:"user_type"`
}

type UserTypeChangedEvent struct {
	UserID  uint64 `json:"user_id"`
	Email   string `json:"email"`
	OldType string `json:"old_type"`
	NewType string `json:"new_type"`
	Reason  string `json:"reason"`
}

type UserLoginEvent struct {
	UserID    uint64 `json:"user_id"`
	Email     string `json:"email"`
	UserAgent string `json:"user_agent"`
	IPAddress string `json:"ip_address"`
}

func (uep *UserEventPublisher) PublishUserCreated(ctx context.Context, user *model.User) error {
	log := uep.logger.WithContext(ctx)

	event := UserCreatedEvent{
		UserID:   user.ID,
		Email:    user.Email,
		Name:     user.Name,
		UserType: string(user.Type),
	}

	key := fmt.Sprintf("user:%d", user.ID)

	if err := uep.eventPublisher.PublishEvent(ctx, uep.config.Kafka.Topics.UserEvents, "user.created", event, key); err != nil {
		log.Errorf("Failed to publish user created event for user %d: %v", user.ID, err)
		return err
	}

	log.Infof("Published user created event for user %d", user.ID)
	return nil
}

func (uep *UserEventPublisher) PublishUserTypeChanged(ctx context.Context, userID uint64, email, oldType, newType, reason string) error {
	log := uep.logger.WithContext(ctx)

	event := UserTypeChangedEvent{
		UserID:  userID,
		Email:   email,
		OldType: oldType,
		NewType: newType,
		Reason:  reason,
	}

	key := fmt.Sprintf("user:%d", userID)

	if err := uep.eventPublisher.PublishEvent(ctx, uep.config.Kafka.Topics.UserEvents, "user.type_changed", event, key); err != nil {
		log.Errorf("Failed to publish user type changed event for user %d: %v", userID, err)
		return err
	}

	log.Infof("Published user type changed event for user %d: %s -> %s", userID, oldType, newType)
	return nil
}

func (uep *UserEventPublisher) PublishUserLogin(ctx context.Context, user *model.User, userAgent, ipAddress string) error {
	log := uep.logger.WithContext(ctx)

	event := UserLoginEvent{
		UserID:    user.ID,
		Email:     user.Email,
		UserAgent: userAgent,
		IPAddress: ipAddress,
	}

	key := fmt.Sprintf("user:%d", user.ID)

	if err := uep.eventPublisher.PublishEvent(ctx, uep.config.Kafka.Topics.UserEvents, "user.login", event, key); err != nil {
		log.Errorf("Failed to publish user login event for user %d: %v", user.ID, err)
		return err
	}

	log.Infof("Published user login event for user %d", user.ID)
	return nil
}

func (uep *UserEventPublisher) Close() error {
	return uep.eventPublisher.Close()
}
