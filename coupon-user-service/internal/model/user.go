package model

import (
	"time"

	"golang.org/x/crypto/bcrypt"
)

type UserType string

const (
	UserTypeNew UserType = "NEW"
	UserTypeVIP UserType = "VIP"
)

type Role string

const (
	RoleUser  Role = "USER"
	RoleAdmin Role = "ADMIN"
)

type User struct {
	ID        uint64    `gorm:"primaryKey;autoIncrement"`
	Name      string    `gorm:"type:varchar(255);not null"`
	Email     string    `gorm:"type:varchar(255);not null;uniqueIndex"`
	Password  string    `gorm:"not null;default:''"`
	Role      Role      `gorm:"type:varchar(10);not null;default:'USER'"`
	Type      UserType  `gorm:"type:varchar(10);not null;default:'NEW'"`
	CreatedAt time.Time `gorm:"not null"`
}

func (User) TableName() string { return "users" }

func NewUser(name, email, password string) (*User, error) {
	hash, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return nil, err
	}

	return &User{
		Name:     name,
		Email:    email,
		Password: string(hash),
		Role:     RoleUser,
		Type:     UserTypeNew,
	}, nil
}

func (u *User) CheckPassword(password string) bool {
	return bcrypt.CompareHashAndPassword([]byte(u.Password), []byte(password)) == nil
}
