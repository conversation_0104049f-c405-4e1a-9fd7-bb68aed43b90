package benchmark

import (
	"context"
	"fmt"
	"os"
	"testing"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/metadata"
	"gorm.io/gorm"

	voucherv1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/voucher/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/benchmark"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/database"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/redis"
)

var (
	voucherGrpcSuite  *benchmark.GRPCBenchmarkSuite
	voucherDBSuite    *benchmark.DatabaseBenchmarkSuite
	voucherCacheSuite *benchmark.CacheBenchmarkSuite
	voucherTestDB     *database.DB
	voucherTestRedis  *redis.Client
	voucherGenerator  *benchmark.TestDataGenerator
)

func TestMain(m *testing.M) {
	if err := setupVoucherBenchmarkEnvironment(); err != nil {
		fmt.Printf("Failed to setup voucher benchmark environment: %v\n", err)
		os.Exit(1)
	}

	code := m.Run()
	cleanupVoucher()
	os.Exit(code)
}

func setupVoucherBenchmarkEnvironment() error {
	cfg, err := config.Load()
	if err != nil {
		return fmt.Errorf("failed to load config: %w", err)
	}

	logger := logging.New("info", "json")
	appMetrics := metrics.New("voucher-service-benchmark")

	voucherTestDB, err = database.NewPostgresDB(&cfg.Database, logger, appMetrics)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}

	voucherTestRedis = redis.NewClient(&cfg.Redis, logger, appMetrics)

	grpcConfig := &benchmark.GRPCBenchmarkConfig{
		ServiceAddress: "localhost:50054",
		ServiceName:    "voucher-service",
		Timeout:        5 * time.Second,
	}

	voucherGrpcSuite, err = benchmark.NewGRPCBenchmarkSuite(grpcConfig)
	if err != nil {
		return fmt.Errorf("failed to create gRPC benchmark suite: %w", err)
	}

	dbConfig := &benchmark.DatabaseBenchmarkConfig{
		ServiceName:    "voucher-service",
		DatabaseType:   "postgres",
		ConnectionPool: 10,
		QueryTimeout:   5 * time.Second,
	}
	voucherDBSuite = benchmark.NewDatabaseBenchmarkSuite(dbConfig, voucherTestDB.DB)

	voucherCacheSuite = benchmark.NewCacheBenchmarkSuite("voucher-service", voucherTestRedis)
	voucherGenerator = benchmark.NewTestDataGenerator()

	return nil
}

func cleanupVoucher() {
	if voucherGrpcSuite != nil {
		voucherGrpcSuite.Close()
	}
	if voucherTestDB != nil {
		sqlDB, _ := voucherTestDB.DB.DB()
		sqlDB.Close()
	}
	if voucherTestRedis != nil {
		voucherTestRedis.Close()
	}
}

func BenchmarkVoucherService_gRPC_CreateVoucher(b *testing.B) {
	voucherGrpcSuite.BenchmarkUnaryCall(b, func(ctx context.Context, conn *grpc.ClientConn) error {
		client := voucherv1.NewVoucherServiceClient(conn)
		ctx = addVoucherAuthMetadata(ctx)

		voucherData := voucherGenerator.GenerateVoucherData()
		req := &voucherv1.CreateVoucherRequest{
			VoucherCode:    voucherData["code"].(string),
			Title:          "Test Voucher",
			Description:    "Benchmark test voucher",
			DiscountTypeId: uint64(voucherData["category_id"].(int)),
			DiscountValue:  voucherData["discount"].(float64),
			UsageMethod:    voucherv1.UsageMethod_USAGE_METHOD_MANUAL,
			MinOrderAmount: 0.0,
		}

		_, err := client.CreateVoucher(ctx, req)
		return err
	})
}

func BenchmarkVoucherService_gRPC_GetVoucher(b *testing.B) {
	voucherGrpcSuite.BenchmarkUnaryCall(b, func(ctx context.Context, conn *grpc.ClientConn) error {
		client := voucherv1.NewVoucherServiceClient(conn)
		ctx = addVoucherAuthMetadata(ctx)

		req := &voucherv1.GetVoucherRequest{
			VoucherId: uint64(voucherGenerator.GenerateVoucherID()),
		}

		_, err := client.GetVoucher(ctx, req)
		return err
	})
}

func BenchmarkVoucherService_gRPC_CheckVoucherEligibility(b *testing.B) {
	voucherGrpcSuite.BenchmarkUnaryCall(b, func(ctx context.Context, conn *grpc.ClientConn) error {
		client := voucherv1.NewVoucherServiceClient(conn)
		ctx = addVoucherAuthMetadata(ctx)

		voucherData := voucherGenerator.GenerateVoucherData()
		req := &voucherv1.CheckVoucherEligibilityRequest{
			VoucherCode: voucherData["code"].(string),
			UserId:      uint64(voucherGenerator.GenerateUserID()),
			OrderAmount: 100.0,
		}

		_, err := client.CheckVoucherEligibility(ctx, req)
		return err
	})
}

func BenchmarkVoucherService_gRPC_GetVoucherByCode(b *testing.B) {
	voucherGrpcSuite.BenchmarkUnaryCall(b, func(ctx context.Context, conn *grpc.ClientConn) error {
		client := voucherv1.NewVoucherServiceClient(conn)
		ctx = addVoucherAuthMetadata(ctx)

		voucherData := voucherGenerator.GenerateVoucherData()
		req := &voucherv1.GetVoucherByCodeRequest{
			VoucherCode: voucherData["code"].(string),
		}

		_, err := client.GetVoucherByCode(ctx, req)
		return err
	})
}

func BenchmarkVoucherService_gRPC_ListVouchers(b *testing.B) {
	voucherGrpcSuite.BenchmarkUnaryCall(b, func(ctx context.Context, conn *grpc.ClientConn) error {
		client := voucherv1.NewVoucherServiceClient(conn)
		ctx = addVoucherAuthMetadata(ctx)

		req := &voucherv1.ListVouchersRequest{}

		_, err := client.ListVouchers(ctx, req)
		return err
	})
}

func BenchmarkVoucherService_Database_VoucherQueries(b *testing.B) {
	queryBenchmarks := benchmark.NewDatabaseQueryBenchmarks(voucherDBSuite)
	queryBenchmarks.BenchmarkVoucherQueries(b)
}

func BenchmarkVoucherService_Database_CreateVoucher(b *testing.B) {
	voucherDBSuite.BenchmarkQuery(b, "create_voucher", func(ctx context.Context, db *gorm.DB) error {
		voucherData := voucherGenerator.GenerateVoucherData()
		voucher := map[string]any{
			"code":         voucherData["code"],
			"discount":     voucherData["discount"],
			"category_id":  voucherData["category_id"],
			"max_uses":     voucherData["max_uses"],
			"current_uses": 0,
			"is_active":    true,
			"expires_at":   time.Now().Add(30 * 24 * time.Hour),
			"created_at":   time.Now(),
			"updated_at":   time.Now(),
		}
		return db.Table("vouchers").Create(voucher).Error
	})
}

func BenchmarkVoucherService_Database_VoucherValidation(b *testing.B) {
	voucherDBSuite.BenchmarkQuery(b, "voucher_validation", func(ctx context.Context, db *gorm.DB) error {
		voucherData := voucherGenerator.GenerateVoucherData()
		var voucher struct {
			ID          int       `gorm:"column:id"`
			Code        string    `gorm:"column:code"`
			Discount    float64   `gorm:"column:discount"`
			MaxUses     int       `gorm:"column:max_uses"`
			CurrentUses int       `gorm:"column:current_uses"`
			IsActive    bool      `gorm:"column:is_active"`
			ExpiresAt   time.Time `gorm:"column:expires_at"`
		}

		return db.Table("vouchers").
			Where("code = ? AND is_active = true AND expires_at > NOW() AND current_uses < max_uses",
				voucherData["code"]).
			First(&voucher).Error
	})
}

func BenchmarkVoucherService_Database_VoucherRedemption(b *testing.B) {
	voucherDBSuite.BenchmarkTransaction(b, "voucher_redemption", func(ctx context.Context, tx *gorm.DB) error {
		voucherData := voucherGenerator.GenerateVoucherData()

		if err := tx.Table("vouchers").
			Where("code = ?", voucherData["code"]).
			Update("current_uses", gorm.Expr("current_uses + 1")).Error; err != nil {
			return err
		}

		redemption := map[string]any{
			"voucher_id":  1, // Simulated voucher ID
			"user_id":     voucherGenerator.GenerateUserID(),
			"order_id":    voucherGenerator.GenerateOrderID(),
			"redeemed_at": time.Now(),
		}

		return tx.Table("voucher_redemptions").Create(redemption).Error
	})
}

func BenchmarkVoucherService_Cache_GetVoucher(b *testing.B) {
	voucherCacheSuite.BenchmarkCacheGet(b, func(ctx context.Context, key string) error {
		_, err := voucherTestRedis.Get(ctx, key)
		return err
	})
}

func BenchmarkVoucherService_Cache_SetVoucher(b *testing.B) {
	voucherCacheSuite.BenchmarkCacheSet(b, func(ctx context.Context, key string, value any) error {
		return voucherTestRedis.Set(ctx, key, value, time.Hour)
	})
}

func BenchmarkVoucherService_Cache_VoucherValidation(b *testing.B) {
	framework := benchmark.NewBenchmarkFramework(&benchmark.BenchmarkConfig{
		ServiceName:    "voucher-service",
		TestName:       "cache_voucher_validation",
		MetricsEnabled: true,
	})

	operation := func(ctx context.Context) error {
		voucherData := voucherGenerator.GenerateVoucherData()
		cacheKey := fmt.Sprintf("voucher:validation:%s", voucherData["code"])

		_, err := voucherTestRedis.Get(ctx, cacheKey)
		if err == nil {
			return nil
		}

		validationResult := map[string]any{
			"valid":    true,
			"discount": voucherData["discount"],
		}

		return voucherTestRedis.Set(ctx, cacheKey, validationResult, 5*time.Minute)
	}

	framework.RunBenchmark(b, operation)
}

func BenchmarkVoucherService_ConcurrentLoad(b *testing.B) {
	scenario := &benchmark.LoadTestScenario{
		Name:        "voucher-service-mixed-load",
		Duration:    30 * time.Second,
		Concurrency: 15,
		RampUpTime:  5 * time.Second,
		Operations: []benchmark.LoadTestOperation{
			{
				Name:   "check_voucher_eligibility",
				Weight: 40,
				Execute: func(ctx context.Context) error {
					ctx = addVoucherAuthMetadata(ctx)

					conn, err := grpc.NewClient("localhost:50055", grpc.WithTransportCredentials(insecure.NewCredentials()))
					if err != nil {
						return err
					}
					defer conn.Close()

					client := voucherv1.NewVoucherServiceClient(conn)
					voucherData := voucherGenerator.GenerateVoucherData()
					req := &voucherv1.CheckVoucherEligibilityRequest{
						VoucherCode: voucherData["code"].(string),
						UserId:      uint64(voucherGenerator.GenerateUserID()),
						OrderAmount: 100.0,
					}
					_, err = client.CheckVoucherEligibility(ctx, req)
					return err
				},
			},
			{
				Name:   "get_voucher",
				Weight: 30,
				Execute: func(ctx context.Context) error {
					ctx = addVoucherAuthMetadata(ctx)

					conn, err := grpc.NewClient("localhost:50055", grpc.WithTransportCredentials(insecure.NewCredentials()))
					if err != nil {
						return err
					}
					defer conn.Close()

					client := voucherv1.NewVoucherServiceClient(conn)
					req := &voucherv1.GetVoucherRequest{
						VoucherId: uint64(voucherGenerator.GenerateVoucherID()),
					}
					_, err = client.GetVoucher(ctx, req)
					return err
				},
			},
			{
				Name:   "list_vouchers",
				Weight: 30,
				Execute: func(ctx context.Context) error {
					ctx = addVoucherAuthMetadata(ctx)

					conn, err := grpc.NewClient("localhost:50055", grpc.WithTransportCredentials(insecure.NewCredentials()))
					if err != nil {
						return err
					}
					defer conn.Close()

					client := voucherv1.NewVoucherServiceClient(conn)
					req := &voucherv1.ListVouchersRequest{}
					_, err = client.ListVouchers(ctx, req)
					return err
				},
			},
		},
	}

	runner := benchmark.NewLoadTestRunner(scenario)
	runner.RunLoadTest(b)
}

func addVoucherAuthMetadata(ctx context.Context) context.Context {
	md := metadata.New(map[string]string{
		"client-id":  "voucher-service-benchmark",
		"client-key": "benchmark-key",
	})
	return metadata.NewOutgoingContext(ctx, md)
}
