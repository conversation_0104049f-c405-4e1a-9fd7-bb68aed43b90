# Voucher Service

This service is responsible for managing all aspects of vouchers, including creation, validation, eligibility checks, and redemption.

## Requirements

- Go 1.24+
- Docker & Docker Compose

## Configuration

Configuration is loaded from `config/config.yaml` and can be overridden by environment variables. The YAML file supports environment variable expansion (e.g., `${DB_HOST}`).

## Local Development

To run the service and its dependencies (Postgres, Redis):

1.  Copy `.env.example` to `.env` and update the values.
2.  Run `make compose-up`.

The service will be available at:

- **gRPC**: `localhost:50054`
- **HTTP (Health/Metrics)**: `localhost:8083`
- **Metrics Path**: `/metrics`

To stop the services:

```bash
make compose-down
```
